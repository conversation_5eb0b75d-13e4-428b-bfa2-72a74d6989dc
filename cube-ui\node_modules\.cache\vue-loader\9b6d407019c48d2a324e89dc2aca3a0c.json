{"remainingRequest": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\src\\views\\index.vue?vue&type=template&id=a83bd3b0&scoped=true", "dependencies": [{"path": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\src\\views\\index.vue", "mtime": 1751901601261}, {"path": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751784291169}, {"path": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751784287584}, {"path": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751784291169}, {"path": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751784287559}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}