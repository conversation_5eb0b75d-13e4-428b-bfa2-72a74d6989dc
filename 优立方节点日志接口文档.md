### **智能体工作流节点日志记录接口**

#### **一、接口基本信息**
- **接口名称**：智能体工作流节点日志记录接口  
- **接口 URL**：`https://u3w.com/cubeServer/mini/saveAINodeLog`  
- **请求方法**：POST  
- **接口描述**：用于记录智能体工作流中节点的执行日志，包括用户信息、节点名称及执行结果等关键数据。

---

#### **二、请求参数**
**（一）请求体参数（JSON 格式）**
| 参数名称     | 数据类型 | 是否必填 | 描述                           |
|--------------|----------|----------|--------------------------------|
| `userId`     | String   | 是       | 用户 ID，标识操作用户的唯一身份 |
| `userPrompt` | String   | 是       | 用户输入内容或指令             |
| `nodeName`   | String   | 是       | 节点名称，记录日志对应的工作流节点 |
| `res`        | String   | 是       | 节点执行结果（输出信息或状态描述） |

**（二）请求示例**
```json
{
  "userId": "user_123456",
  "userPrompt": "请分析当前市场趋势",
  "nodeName": "市场分析节点",
  "res": "成功获取市场数据并生成分析报告"
}
```

---

#### **三、响应参数**
**（一）响应体参数（JSON 格式）**
| 参数名称 | 数据类型 | 描述                     |
|----------|----------|--------------------------|
| `code`   | Integer  | 状态码，标识请求处理结果 |
| `msg`    | String   | 状态信息（详细说明）     |

**（二）响应状态码说明**
| 状态码 | 状态信息            | 描述                             |
|--------|---------------------|----------------------------------|
| 200    | OK                  | 请求成功，节点日志已记录         |
| 400    | Bad Request         | 请求参数错误（缺失、格式不正确） |
| 500    | Internal Server Error | 服务器内部错误                   |

**（三）响应示例**
1. **请求成功**：
   ```json
   {
     "code": 200,
     "msg": "日志记录成功"
   }
   ```
2. **请求方法错误**：
   ```json
   {
     "code": 500,
     "msg": "Request method 'GET' not supported"
   }
   ```
3. **参数缺失**：
   ```json
   {
     "code": 400,
     "msg": "缺少必填参数userId"
   }
   ```

---

#### **四、接口调用注意事项**
1. **请求方法**：必须使用 POST 方法调用接口，其他方法（如 GET）会返回错误。  
2. **参数格式**：请求体需为合法 JSON，参数名称正确且值类型符合要求。  
3. **必填参数**：`userId`、`userPrompt`、`nodeName`、`res` 均为必填项，缺失会导致失败。  
4. **字符编码**：请求体和响应体均使用 UTF-8 编码。  
5. **幂等性**：接口不保证幂等性，多次相同请求可能导致日志重复记录。  
6. **错误处理**：根据返回的状态码和信息进行错误处理，确保系统稳定性。