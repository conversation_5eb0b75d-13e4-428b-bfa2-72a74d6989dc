<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="f0b922d8-7e98-49ff-86f3-79b2a3bdd18d" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/workspace.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-admin/target/classes/com/tencent/wework/RSAEncrypt.class" beforeDir="false" afterPath="$PROJECT_DIR$/cube-admin/target/classes/com/tencent/wework/RSAEncrypt.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-admin/target/classes/mapper/AIGCMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/cube-admin/target/classes/mapper/AIGCMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-engine/target/classes/application.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/cube-engine/target/classes/application.yaml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-engine/target/classes/com/playwright/controller/AIGCController.class" beforeDir="false" afterPath="$PROJECT_DIR$/cube-engine/target/classes/com/playwright/controller/AIGCController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-engine/target/classes/com/playwright/controller/BrowserController.class" beforeDir="false" afterPath="$PROJECT_DIR$/cube-engine/target/classes/com/playwright/controller/BrowserController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-engine/target/classes/com/playwright/websocket/WebSocketClientService$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/cube-engine/target/classes/com/playwright/websocket/WebSocketClientService$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-engine/target/classes/com/playwright/websocket/WebSocketClientService.class" beforeDir="false" afterPath="$PROJECT_DIR$/cube-engine/target/classes/com/playwright/websocket/WebSocketClientService.class" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 8
}</component>
  <component name="ProjectId" id="2zUPFpYW1Lp4hbwQDVrxxtOVEiq" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Maven.cube [clean].executor": "Run",
    "Maven.cube [package].executor": "Run",
    "Maven.cube-admin [clean].executor": "Run",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "Spring Boot.App.executor": "Run",
    "Spring Boot.CubeApplication.executor": "Run",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "D:/JavaWorkSpace/U3W-AI",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "模块",
    "project.structure.proportion": "0.15",
    "project.structure.side.proportion": "0.2",
    "settings.editor.selected.configurable": "preferences.lookFeel",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager" selected="Spring Boot.CubeApplication">
    <configuration name="App" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="U3W" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.playwright.App" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="CubeApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="cube-admin" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.cube.CubeApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="f0b922d8-7e98-49ff-86f3-79b2a3bdd18d" name="更改" comment="" />
      <created>1751782577516</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751782577516</updated>
      <workItem from="1751782579565" duration="7035000" />
      <workItem from="1751792197595" duration="211000" />
      <workItem from="1751792413403" duration="141000" />
      <workItem from="1751792558081" duration="4619000" />
      <workItem from="1751797966192" duration="179000" />
      <workItem from="1751798149712" duration="105000" />
      <workItem from="1751798258040" duration="11142000" />
      <workItem from="1751854125590" duration="21318000" />
      <workItem from="1751937650373" duration="4529000" />
      <workItem from="1752146452099" duration="21000" />
      <workItem from="1752146477808" duration="2539000" />
      <workItem from="1752201036912" duration="378000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>