server:
  port: 8083


cube:
  url: http://127.0.0.1:8081/aigc
  wssurl: ws://127.0.0.1:8081/websocket?clientId=play-你的主机ID  #主机ID建议使用字母+数字组合，例如play-user01
  datadir: C:\AGI\user-data-dir
  uploadurl: http://127.0.0.1:8081/common/upload

#如果配置url是加了https证书的域名，则需要改为wss://你的域名/cubeServer/websocket?clientId=play-你的主机ID
#https域名示例：
#cube:
#  url: https://你的域名/cubeServer/aigc
#  wssurl: wss://你的域名/cubeServer/websocket?clientId=play-你的主机ID
#  datadir: C:\AGI\user-data-dir   用户数据存放目录
#  uploadurl: https://你的域名/cubeServer/common/upload

#如果配置url是ip，则需要去掉cubeServer这一层，wssurl填写为ws://你的ip:端口//websocket?clientId=play-你的主机ID
#ip示例：
#cube:
#  url: http://你的ip:端口/aigc
#  wssurl: ws://你的ip:端口/websocket?clientId=play-你的主机ID
#  datadir: C:\AGI\user-data-dir   用户数据存放目录
#  uploadurl: http://你的ip:端口/common/upload
