{"remainingRequest": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\src\\views\\wechat\\chrome\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\src\\views\\wechat\\chrome\\index.vue", "mtime": 1751906448186}, {"path": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\babel.config.js", "mtime": 1751782516642}, {"path": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751784291169}, {"path": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751784291203}, {"path": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751784291169}, {"path": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751784287559}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_marked", "require", "_aigc", "_uuid", "_websocket", "_interopRequireDefault", "_store", "_turndown", "name", "data", "userId", "store", "state", "user", "id", "corpId", "corp_id", "chatId", "uuidv4", "expandedHistoryItems", "userInfoReq", "userPrompt", "taskId", "roles", "toneChatId", "ybDsChatId", "dbChatId", "isNewChat", "jsonRpcReqest", "jsonrpc", "method", "params", "aiList", "avatar", "capabilities", "selectedCapabilities", "enabled", "status", "progressLogs", "isExpanded", "label", "value", "promptInput", "taskStarted", "autoPlay", "screenshots", "results", "activeResultTab", "activeCollapses", "showImageDialog", "currentLargeImage", "enabledAIs", "turndownService", "TurndownService", "headingStyle", "codeBlockStyle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scoreDialogVisible", "selectedResults", "scorePrompt", "historyDrawerVisible", "chatHistory", "pushOfficeNum", "pushingToWechat", "computed", "canSend", "trim", "length", "some", "ai", "canScore", "groupedHistory", "_this", "groups", "chatGroups", "for<PERSON>ach", "item", "push", "Object", "values", "chatGroup", "sort", "a", "b", "Date", "createTime", "parentItem", "date", "getHistoryDate", "_objectSpread2", "default", "isParent", "children", "slice", "map", "child", "created", "console", "log", "initWebSocket", "loadChatHistory", "loadLastChat", "methods", "sendPrompt", "_this2", "filter", "$set", "includes", "message", "then", "res", "code", "uni", "showToast", "title", "messages", "icon", "duration", "toggleCapability", "capabilityValue", "index", "indexOf", "newCapabilities", "_toConsumableArray2", "modelIndex1", "modelIndex2", "splice", "$forceUpdate", "getStatusText", "getStatusIcon", "renderMarkdown", "text", "marked", "htmlToText", "html", "tempDiv", "document", "createElement", "innerHTML", "textContent", "innerText", "htmlToMarkdown", "turndown", "copyResult", "content", "plainText", "textarea", "body", "append<PERSON><PERSON><PERSON>", "select", "execCommand", "<PERSON><PERSON><PERSON><PERSON>", "$message", "success", "exportResult", "result", "markdown", "blob", "Blob", "type", "link", "href", "URL", "createObjectURL", "download", "concat", "aiName", "toISOString", "click", "revokeObjectURL", "openShareUrl", "shareUrl", "window", "open", "warning", "showLargeImage", "imageUrl", "_this3", "currentIndex", "$nextTick", "carousel", "$el", "querySelector", "__vue__", "setActiveItem", "closeLargeImage", "_this4", "wsUrl", "process", "env", "VUE_APP_WS_API", "websocketClient", "connect", "event", "handleWebSocketMessage", "error", "datastr", "dataObj", "JSON", "parse", "targetAI", "find", "unshift", "timestamp", "isCompleted", "url", "wkpfAI", "draftContent", "shareImgUrl", "saveHistory", "resultIndex", "findIndex", "r", "closeWebSocket", "close", "sendMessage", "_this5", "send", "scrollToBottom", "toggleAIExpansion", "formatTime", "toLocaleTimeString", "hour", "minute", "second", "hour12", "showScoreDialog", "handleScore", "_this6", "selectedContents", "plainContent", "join", "fullPrompt", "scoreRequest", "existIndex", "wkpf", "showHistoryDrawer", "handleHistoryDrawerClose", "isAll", "_this7", "_asyncToGenerator2", "_regenerator2", "m", "_callee", "_t", "w", "_context", "n", "p", "getChatHistory", "v", "formatHistoryTime", "today", "yesterday", "setDate", "getDate", "twoDaysAgo", "threeDaysAgo", "toDateString", "toLocaleDateString", "year", "month", "day", "loadHistoryItem", "historyData", "_this8", "_callee2", "_t2", "_context2", "saveUserChatData", "stringify", "toggleHistoryExpansion", "createNewChat", "_this9", "_callee3", "lastChat", "_t3", "_context3", "isImageFile", "imageExtensions", "url<PERSON><PERSON><PERSON>", "toLowerCase", "ext", "isPdfFile", "getImageStyle", "widthMap", "width", "height", "handlePushToWechat", "_this0", "contentText", "num", "pushAutoOffice", "msg", "catch", "finally"], "sources": ["src/views/wechat/chrome/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"ai-management-platform\">\r\n    <!-- 顶部导航区 -->\r\n    <div class=\"top-nav\">\r\n      <div class=\"logo-area\">\r\n        <img src=\"../../../assets/ai/logo.png\" alt=\"Logo\" class=\"logo\">\r\n        <h1 class=\"platform-title\">主机</h1>\r\n      </div>\r\n      <div class=\"nav-buttons\">\r\n        <el-button type=\"primary\" size=\"small\" @click=\"createNewChat\">\r\n          <i class=\"el-icon-plus\"></i>\r\n          创建新对话\r\n        </el-button>\r\n        <div class=\"history-button\">\r\n          <el-button type=\"text\" @click=\"showHistoryDrawer\">\r\n            <img :src=\"require('../../../assets/ai/celan.png')\" alt=\"历史记录\" class=\"history-icon\">\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 历史记录抽屉 -->\r\n    <el-drawer title=\"历史会话记录\" :visible.sync=\"historyDrawerVisible\" direction=\"rtl\" size=\"30%\"\r\n      :before-close=\"handleHistoryDrawerClose\">\r\n      <div class=\"history-content\">\r\n        <div v-for=\"(group, date) in groupedHistory\" :key=\"date\" class=\"history-group\">\r\n          <div class=\"history-date\">{{ date }}</div>\r\n          <div class=\"history-list\">\r\n            <div v-for=\"(item, index) in group\" :key=\"index\" class=\"history-item\">\r\n              <div class=\"history-parent\" @click=\"loadHistoryItem(item)\">\r\n                <div class=\"history-header\">\r\n                  <i :class=\"['el-icon-arrow-right', {'is-expanded': item.isExpanded}]\"\r\n                    @click.stop=\"toggleHistoryExpansion(item)\"></i>\r\n                  <div class=\"history-prompt\">{{ item.userPrompt }}</div>\r\n                </div>\r\n                <div class=\"history-time\">{{ formatHistoryTime(item.createTime) }}</div>\r\n              </div>\r\n              <div v-if=\"item.children && item.children.length > 0 && item.isExpanded\" class=\"history-children\">\r\n                <div v-for=\"(child, childIndex) in item.children\" :key=\"childIndex\" class=\"history-child-item\"\r\n                  @click=\"loadHistoryItem(child)\">\r\n                  <div class=\"history-prompt\">{{ child.userPrompt }}</div>\r\n                  <div class=\"history-time\">{{ formatHistoryTime(child.createTime) }}</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-drawer>\r\n\r\n    <div class=\"main-content\">\r\n      <el-collapse v-model=\"activeCollapses\">\r\n        <el-collapse-item title=\"AI选择配置\" name=\"ai-selection\">\r\n          <div class=\"ai-selection-section\">\r\n            <div class=\"ai-cards\">\r\n              <el-card v-for=\"(ai, index) in aiList\" :key=\"index\" class=\"ai-card\" shadow=\"hover\">\r\n                <div class=\"ai-card-header\">\r\n                  <div class=\"ai-left\">\r\n                    <div class=\"ai-avatar\">\r\n                      <img :src=\"ai.avatar\" alt=\"AI头像\">\r\n                    </div>\r\n                    <div class=\"ai-name\">{{ ai.name }}</div>\r\n                  </div>\r\n                  <div class=\"ai-status\">\r\n                    <el-switch v-model=\"ai.enabled\" active-color=\"#13ce66\" inactive-color=\"#ff4949\">\r\n                    </el-switch>\r\n                  </div>\r\n                </div>\r\n                <div class=\"ai-capabilities\" v-if=\"ai.capabilities && ai.capabilities.length > 0\">\r\n                  <div class=\"button-capability-group\">\r\n                    <el-button v-for=\"capability in ai.capabilities\" :key=\"capability.value\" size=\"mini\"\r\n                      :type=\"ai.selectedCapabilities.includes(capability.value) ? 'primary' : 'info'\"\r\n                      :disabled=\"!ai.enabled\" :plain=\"!ai.selectedCapabilities.includes(capability.value)\"\r\n                      @click=\"toggleCapability(ai, capability.value)\" class=\"capability-button\">\r\n                      {{ capability.label }}\r\n                    </el-button>\r\n                  </div>\r\n                </div>\r\n              </el-card>\r\n            </div>\r\n          </div>\r\n        </el-collapse-item>\r\n\r\n        <!-- 提示词输入区 -->\r\n        <el-collapse-item title=\"提示词输入\" name=\"prompt-input\">\r\n          <div class=\"prompt-input-section\">\r\n            <el-input type=\"textarea\" :rows=\"5\" placeholder=\"请输入提示词，支持Markdown格式\" v-model=\"promptInput\" resize=\"none\"\r\n              class=\"prompt-input\">\r\n            </el-input>\r\n            <div class=\"prompt-footer\">\r\n              <div class=\"word-count\">字数统计: {{ promptInput.length }}</div>\r\n              <el-button type=\"primary\" @click=\"sendPrompt\" :disabled=\"!canSend\" class=\"send-button\">\r\n                发送\r\n              </el-button>\r\n            </div>\r\n          </div>\r\n        </el-collapse-item>\r\n      </el-collapse>\r\n\r\n      <!-- 执行状态展示区 -->\r\n      <div class=\"execution-status-section\" v-if=\"taskStarted\">\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-card class=\"task-flow-card\">\r\n              <div slot=\"header\" class=\"card-header\">\r\n                <span>任务流程</span>\r\n              </div>\r\n              <div class=\"task-flow\">\r\n                <div v-for=\"(ai, index) in enabledAIs\" :key=\"index\" class=\"task-item\">\r\n                  <div class=\"task-header\" @click=\"toggleAIExpansion(ai)\">\r\n                    <div class=\"header-left\">\r\n                      <i :class=\"['el-icon-arrow-right', {'is-expanded': ai.isExpanded}]\"></i>\r\n                      <span class=\"ai-name\">{{ ai.name }}</span>\r\n                    </div>\r\n                    <div class=\"header-right\">\r\n                      <span class=\"status-text\">{{ getStatusText(ai.status) }}</span>\r\n                      <i :class=\"getStatusIcon(ai.status)\" class=\"status-icon\"></i>\r\n                    </div>\r\n                  </div>\r\n                  <!-- 添加进度轨迹 -->\r\n                  <div class=\"progress-timeline\" v-if=\"ai.progressLogs.length > 0 && ai.isExpanded\">\r\n                    <div class=\"timeline-scroll\">\r\n                      <div v-for=\"(log, logIndex) in ai.progressLogs\" :key=\"logIndex\" class=\"progress-item\" :class=\"{\r\n                             'completed': log.isCompleted || logIndex > 0,\r\n                             'current': !log.isCompleted && logIndex === 0\r\n                           }\">\r\n                        <div class=\"progress-dot\"></div>\r\n                        <div class=\"progress-line\" v-if=\"logIndex < ai.progressLogs.length - 1\"></div>\r\n                        <div class=\"progress-content\">\r\n                          <div class=\"progress-time\">{{ formatTime(log.timestamp) }}</div>\r\n                          <div class=\"progress-text\">{{ log.content }}</div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-card class=\"screenshots-card\">\r\n              <div slot=\"header\" class=\"card-header\">\r\n                <span>主机可视化</span>\r\n                <div class=\"controls\">\r\n                  <el-switch v-model=\"autoPlay\" active-text=\"自动轮播\" inactive-text=\"手动切换\">\r\n                  </el-switch>\r\n                </div>\r\n              </div>\r\n              <div class=\"screenshots\">\r\n                <el-carousel :interval=\"3000\" :autoplay=\"false\" indicator-position=\"outside\" height=\"700px\">\r\n                  <el-carousel-item v-for=\"(screenshot, index) in screenshots\" :key=\"index\">\r\n                    <img :src=\"screenshot\" alt=\"执行截图\" class=\"screenshot-image\" @click=\"showLargeImage(screenshot)\">\r\n                  </el-carousel-item>\r\n                </el-carousel>\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n        </el-row>\r\n      </div>\r\n\r\n      <!-- 结果展示区 -->\r\n      <div class=\"results-section\" v-if=\"results.length > 0\">\r\n        <div class=\"section-header\">\r\n          <h2 class=\"section-title\">执行结果</h2>\r\n          <el-button type=\"primary\" @click=\"showScoreDialog\" size=\"small\">\r\n            智能评分\r\n          </el-button>\r\n        </div>\r\n        <el-tabs v-model=\"activeResultTab\" type=\"card\">\r\n          <el-tab-pane v-for=\"(result, index) in results\" :key=\"index\" :label=\"result.aiName\" :name=\"'result-' + index\">\r\n            <div class=\"result-content\">\r\n              <div class=\"result-header\" v-if=\"result.shareUrl\">\r\n                <div class=\"result-title\">{{ result.aiName }}的执行结果</div>\r\n                <div class=\"result-buttons\">\r\n                  <el-button size=\"mini\" type=\"primary\" icon=\"el-icon-link\" @click=\"openShareUrl(result.shareUrl)\"\r\n                    class=\"share-link-btn\">\r\n                    查看原链接\r\n                  </el-button>\r\n                  <el-button size=\"mini\" type=\"success\" icon=\"el-icon-s-promotion\" @click=\"handlePushToWechat(result)\"\r\n                    class=\"push-wechat-btn\" :loading=\"pushingToWechat\" :disabled=\"pushingToWechat\">\r\n                    投递到公众号\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n              <!-- 如果有shareImgUrl则渲染图片或PDF，否则渲染markdown -->\r\n              <div v-if=\"result.shareImgUrl\" class=\"share-content\">\r\n                <!-- 渲染图片 -->\r\n                <img v-if=\"isImageFile(result.shareImgUrl)\" :src=\"result.shareImgUrl\" alt=\"分享图片\" class=\"share-image\"\r\n                  :style=\"getImageStyle(result.aiName)\">\r\n                <!-- 渲染PDF -->\r\n                <iframe v-else-if=\"isPdfFile(result.shareImgUrl)\" :src=\"result.shareImgUrl\" class=\"share-pdf\"\r\n                  frameborder=\"0\">\r\n                </iframe>\r\n                <!-- 其他文件类型显示链接 -->\r\n                <div v-else class=\"share-file\">\r\n                  <el-button type=\"primary\" icon=\"el-icon-document\" @click=\"openShareUrl(result.shareImgUrl)\">\r\n                    查看文件\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n              <div v-else class=\"markdown-content\" v-html=\"renderMarkdown(result.content)\"></div>\r\n              <div class=\"action-buttons\">\r\n                <el-button size=\"small\" type=\"primary\" @click=\"copyResult(result.content)\">复制（纯文本）</el-button>\r\n                <el-button size=\"small\" type=\"success\" @click=\"exportResult(result)\">导出（MD文件）</el-button>\r\n              </div>\r\n            </div>\r\n          </el-tab-pane>\r\n        </el-tabs>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 大图查看对话框 -->\r\n    <el-dialog :visible.sync=\"showImageDialog\" width=\"90%\" :show-close=\"true\" :modal=\"true\" center class=\"image-dialog\"\r\n      :append-to-body=\"true\" @close=\"closeLargeImage\">\r\n      <div class=\"large-image-container\">\r\n        <!-- 如果是单张分享图片，直接显示 -->\r\n        <div v-if=\"currentLargeImage && !screenshots.includes(currentLargeImage)\" class=\"single-image-container\">\r\n          <img :src=\"currentLargeImage\" alt=\"大图\" class=\"large-image\">\r\n        </div>\r\n        <!-- 如果是截图轮播 -->\r\n        <el-carousel v-else :interval=\"3000\" :autoplay=\"false\" indicator-position=\"outside\" height=\"80vh\">\r\n          <el-carousel-item v-for=\"(screenshot, index) in screenshots\" :key=\"index\">\r\n            <img :src=\"screenshot\" alt=\"大图\" class=\"large-image\">\r\n          </el-carousel-item>\r\n        </el-carousel>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 评分弹窗 -->\r\n    <el-dialog title=\"智能评分\" :visible.sync=\"scoreDialogVisible\" width=\"60%\" height=\"65%\" :close-on-click-modal=\"false\"\r\n      class=\"score-dialog\">\r\n      <div class=\"score-dialog-content\">\r\n        <div class=\"score-prompt-section\">\r\n          <h3>评分提示词：</h3>\r\n          <el-input type=\"textarea\" :rows=\"10\" placeholder=\"请输入评分提示词，例如：请从内容质量、逻辑性、创新性等方面进行评分\" v-model=\"scorePrompt\"\r\n            resize=\"none\" class=\"score-prompt-input\">\r\n          </el-input>\r\n        </div>\r\n        <div class=\"selected-results\">\r\n          <h3>选择要评分的内容：</h3>\r\n          <el-checkbox-group v-model=\"selectedResults\">\r\n            <el-checkbox v-for=\"(result, index) in results\" :key=\"index\" :label=\"result.aiName\" class=\"result-checkbox\">\r\n              {{ result.aiName }}\r\n            </el-checkbox>\r\n          </el-checkbox-group>\r\n        </div>\r\n\r\n\r\n      </div>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"scoreDialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"handleScore\" :disabled=\"!canScore\">\r\n          开始评分\r\n        </el-button>\r\n      </span>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {marked} from 'marked';\r\nimport {message, saveUserChatData, getChatHistory, pushAutoOffice} from \"@/api/wechat/aigc\";\r\nimport {\r\n\t\tv4 as uuidv4\r\n\t} from 'uuid';\r\nimport websocketClient from '@/utils/websocket';\r\nimport store from '@/store';\r\nimport TurndownService from 'turndown';\r\n\r\nexport default {\r\n  name: 'AIManagementPlatform',\r\n  data() {\r\n    return {\r\n      userId: store.state.user.id,\r\n      corpId: store.state.user.corp_id,\r\n      chatId: uuidv4(),\r\n      expandedHistoryItems: {},\r\n      userInfoReq: {\r\n        userPrompt: '',\r\n        userId: '',\r\n        corpId: '',\r\n        taskId: '',\r\n        roles: '',\r\n        toneChatId: '',\r\n        ybDsChatId: '',\r\n        dbChatId: '',\r\n        isNewChat: true\r\n      },\r\n      jsonRpcReqest: {\r\n        jsonrpc: '2.0',\r\n        id: uuidv4(),\r\n        method: '',\r\n        params: {}\r\n      },\r\n      aiList: [\r\n        {\r\n          name: 'TurboS@元器',\r\n          avatar: require('../../../assets/ai/yuanbao.png'),\r\n          capabilities: [],\r\n          selectedCapabilities: [],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        },\r\n        {\r\n          name: 'TurboS长文版@元器',\r\n          avatar: require('../../../assets/ai/yuanbao.png'),\r\n          capabilities: [],\r\n          selectedCapabilities: [],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        },\r\n        // {\r\n        //   name: 'MiniMax@元器',\r\n        //   avatar: require('../../../assets/ai/yuanbao.png'),\r\n        //   capabilities: [],\r\n        //   selectedCapabilities: [],\r\n        //   enabled: true,\r\n        //   status: 'idle',\r\n        //   progressLogs: [],\r\n        //   isExpanded: true\r\n        // },\r\n        // {\r\n        //   name: '搜狗搜索@元器',\r\n        //   avatar: require('../../../assets/ai/yuanbao.png'),\r\n        //   capabilities: [],\r\n        //   selectedCapabilities: [],\r\n        //   enabled: true,\r\n        //   status: 'idle',\r\n        //   progressLogs: [],\r\n        //   isExpanded: true\r\n        // },\r\n        // {\r\n        //   name: 'KIMI@元器',\r\n        //   avatar: require('../../../assets/ai/yuanbao.png'),\r\n        //   capabilities: [],\r\n        //   selectedCapabilities: [],\r\n        //   enabled: true,\r\n        //   status: 'idle',\r\n        //   progressLogs: [],\r\n        //   isExpanded: true\r\n        // },\r\n        {\r\n          name: '腾讯元宝T1',\r\n          avatar: require('../../../assets/ai/yuanbao.png'),\r\n          capabilities: [\r\n            { label: '深度思考', value: 'deep_thinking' },\r\n            { label: '联网搜索', value: 'web_search' }\r\n          ],\r\n          selectedCapabilities: ['deep_thinking','web_search'],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        },\r\n        {\r\n          name: '腾讯元宝DS',\r\n          avatar: require('../../../assets/ai/yuanbao.png'),\r\n          capabilities: [\r\n            { label: '深度思考', value: 'deep_thinking' },\r\n            { label: '联网搜索', value: 'web_search' }\r\n          ],\r\n          selectedCapabilities: ['deep_thinking','web_search'],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        },\r\n        {\r\n          name: '豆包',\r\n          avatar: require('../../../assets/ai/豆包.png'),\r\n          capabilities: [\r\n            { label: '深度思考', value: 'deep_thinking' }\r\n          ],\r\n          selectedCapabilities: ['deep_thinking'],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        },\r\n        {\r\n          name: '百度AI',\r\n          avatar: require('../../../assets/ai/baidu.png'),\r\n          capabilities: [],\r\n          selectedCapabilities: [],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        }\r\n      ],\r\n      promptInput: '',\r\n      taskStarted: false,\r\n      autoPlay: false,\r\n      screenshots: [],\r\n      results: [],\r\n      activeResultTab: 'result-0',\r\n      activeCollapses: ['ai-selection', 'prompt-input'], // 默认展开这两个区域\r\n      showImageDialog: false,\r\n      currentLargeImage: '',\r\n      enabledAIs: [],\r\n      turndownService: new TurndownService({\r\n        headingStyle: 'atx',\r\n        codeBlockStyle: 'fenced',\r\n        emDelimiter: '*'\r\n      }),\r\n      scoreDialogVisible: false,\r\n      selectedResults: [],\r\n      scorePrompt: `请你深度阅读以下几篇公众号章，从多个维度进行逐项打分，输出评分结果。并在以下各篇文章的基础上博采众长，综合整理一篇更全面的文章。`,\r\n      historyDrawerVisible: false,\r\n      chatHistory: [],\r\n      pushOfficeNum: 0, // 投递到公众号的递增编号\r\n      pushingToWechat: false, // 投递到公众号的loading状态\r\n    };\r\n  },\r\n  computed: {\r\n    canSend() {\r\n      return this.promptInput.trim().length > 0 && this.aiList.some(ai => ai.enabled);\r\n    },\r\n    canScore() {\r\n      return this.selectedResults.length > 0 && this.scorePrompt.trim().length > 0;\r\n    },\r\n    groupedHistory() {\r\n      const groups = {};\r\n      const chatGroups = {};\r\n\r\n      // 首先按chatId分组\r\n      this.chatHistory.forEach(item => {\r\n        if (!chatGroups[item.chatId]) {\r\n          chatGroups[item.chatId] = [];\r\n        }\r\n        chatGroups[item.chatId].push(item);\r\n      });\r\n\r\n      // 然后按日期分组，并处理父子关系\r\n      Object.values(chatGroups).forEach(chatGroup => {\r\n        // 按时间排序\r\n        chatGroup.sort((a, b) => new Date(a.createTime) - new Date(b.createTime));\r\n\r\n        // 获取最早的记录作为父级\r\n        const parentItem = chatGroup[0];\r\n        const date = this.getHistoryDate(parentItem.createTime);\r\n\r\n        if (!groups[date]) {\r\n          groups[date] = [];\r\n        }\r\n\r\n        // 添加父级记录\r\n        groups[date].push({\r\n          ...parentItem,\r\n          isParent: true,\r\n          isExpanded: this.expandedHistoryItems[parentItem.chatId] || false,\r\n          children: chatGroup.slice(1).map(child => ({\r\n            ...child,\r\n            isParent: false\r\n          }))\r\n        });\r\n      });\r\n\r\n      return groups;\r\n    }\r\n  },\r\n  created() {\r\n    console.log(this.userId);\r\n    console.log(this.corpId);\r\n    this.initWebSocket(this.userId);\r\n    this.loadChatHistory(0); // 加载历史记录\r\n    this.loadLastChat(); // 加载上次会话\r\n  },\r\n  methods: {\r\n    sendPrompt() {\r\n      if (!this.canSend) return;\r\n\r\n      this.screenshots =[];\r\n      // 折叠所有区域\r\n      this.activeCollapses = [];\r\n\r\n      this.taskStarted = true;\r\n      this.results = []; // 清空之前的结果\r\n\r\n      this.userInfoReq.roles = '';\r\n\r\n\r\n      this.userInfoReq.taskId = uuidv4();\r\n      this.userInfoReq.userId = this.userId;\r\n      this.userInfoReq.corpId = this.corpId;\r\n      this.userInfoReq.userPrompt = this.promptInput;\r\n\r\n      // 获取启用的AI列表及其状态\r\n      this.enabledAIs = this.aiList.filter(ai => ai.enabled);\r\n\r\n      // 将所有启用的AI状态设置为运行中\r\n      this.enabledAIs.forEach(ai => {\r\n        this.$set(ai, 'status', 'running');\r\n      });\r\n\r\n      this.enabledAIs.forEach(ai => {\r\n        if(ai.name === '腾讯元宝T1'){\r\n          this.userInfoReq.roles = this.userInfoReq.roles + 'yb-hunyuan-pt,';\r\n          if(ai.selectedCapabilities.includes(\"deep_thinking\")){\r\n            this.userInfoReq.roles = this.userInfoReq.roles + 'yb-hunyuan-sdsk,';\r\n          }\r\n          if(ai.selectedCapabilities.includes(\"web_search\")){\r\n            this.userInfoReq.roles = this.userInfoReq.roles + 'yb-hunyuan-lwss,';\r\n          }\r\n        }\r\n        if(ai.name === '腾讯元宝DS'){\r\n          this.userInfoReq.roles = this.userInfoReq.roles + 'yb-deepseek-pt,';\r\n          if(ai.selectedCapabilities.includes(\"deep_thinking\")){\r\n            this.userInfoReq.roles = this.userInfoReq.roles + 'yb-deepseek-sdsk,';\r\n          }\r\n          if(ai.selectedCapabilities.includes(\"web_search\")){\r\n            this.userInfoReq.roles = this.userInfoReq.roles + 'yb-deepseek-lwss,';\r\n          }\r\n        }\r\n        if(ai.name === 'TurboS@元器'){\r\n          this.userInfoReq.roles = this.userInfoReq.roles + 'cube-trubos-agent,';\r\n        }\r\n        if(ai.name === 'TurboS长文版@元器'){\r\n          this.userInfoReq.roles = this.userInfoReq.roles + 'cube-turbos-large-agent,';\r\n        }\r\n        if(ai.name === 'MiniMax@元器'){\r\n          this.userInfoReq.roles = this.userInfoReq.roles + 'cube-mini-max-agent,';\r\n        }\r\n        // if(ai.name === '搜狗搜索@元器'){\r\n        //   this.userInfoReq.roles = this.userInfoReq.roles + 'cube-sogou-agent,';\r\n        // }\r\n        // if(ai.name === 'KIMI@元器'){\r\n        //   this.userInfoReq.roles = this.userInfoReq.roles + 'cube-lwss-agent,';\r\n        // }\r\n        if(ai.name === '豆包'){\r\n          this.userInfoReq.roles = this.userInfoReq.roles + 'zj-db,';\r\n          if (ai.selectedCapabilities.includes(\"deep_thinking\")) {\r\n            this.userInfoReq.roles = this.userInfoReq.roles + 'zj-db-sdsk,';\r\n          }\r\n        }\r\n        if(ai.name === '百度AI'){\r\n          this.userInfoReq.roles = this.userInfoReq.roles + 'wx-';\r\n          // 添加模型选择\r\n          if (ai.selectedCapabilities.includes(\"deepseek-r1\")) {\r\n            this.userInfoReq.roles = this.userInfoReq.roles + 'deepseek-r1,';\r\n          } else if (ai.selectedCapabilities.includes(\"deepseek-v3\")) {\r\n            this.userInfoReq.roles = this.userInfoReq.roles + 'deepseek-v3,';\r\n          } else {\r\n            this.userInfoReq.roles = this.userInfoReq.roles + 'deepseek-r1,'; // 默认模型\r\n          }\r\n          // 添加联网搜索选项\r\n          if (ai.selectedCapabilities.includes(\"internet-search\")) {\r\n            this.userInfoReq.roles = this.userInfoReq.roles + 'internet-search,';\r\n          }\r\n        }\r\n      });\r\n\r\n      console.log(\"参数：\", this.userInfoReq)\r\n\r\n      //调用后端接口\r\n      this.jsonRpcReqest.method = \"使用F8S\"\r\n      this.jsonRpcReqest.params = this.userInfoReq\r\n      this.message(this.jsonRpcReqest)\r\n      this.userInfoReq.isNewChat = false;\r\n    },\r\n\r\n    message(data) {\r\n      message(data).then(res => {\r\n        if (res.code == 201) {\r\n          uni.showToast({\r\n            title: res.messages,\r\n            icon: 'none',\r\n            duration: 1500,\r\n          });\r\n        }\r\n      })\r\n\r\n    },\r\n    toggleCapability(ai, capabilityValue) {\r\n      if (!ai.enabled) return;\r\n\r\n      const index = ai.selectedCapabilities.indexOf(capabilityValue);\r\n      console.log('切换前:', ai.selectedCapabilities);\r\n\r\n      // 百度AI的模型选择逻辑：DeepSeek-R1和DeepSeek-V3只能选一个\r\n      if (ai.name === '百度AI' && (capabilityValue === 'deepseek-r1' || capabilityValue === 'deepseek-v3')) {\r\n        const newCapabilities = [...ai.selectedCapabilities];\r\n        // 移除所有模型选择\r\n        const modelIndex1 = newCapabilities.indexOf('deepseek-r1');\r\n        const modelIndex2 = newCapabilities.indexOf('deepseek-v3');\r\n        if (modelIndex1 !== -1) newCapabilities.splice(modelIndex1, 1);\r\n        if (modelIndex2 !== -1) newCapabilities.splice(modelIndex2, 1);\r\n\r\n        // 如果当前选择的不是已选中的模型，则添加新选择\r\n        if (index === -1) {\r\n          newCapabilities.push(capabilityValue);\r\n        }\r\n        // 如果点击的是已选中的模型，则不添加（保持取消选择的效果）\r\n\r\n        this.$set(ai, 'selectedCapabilities', newCapabilities);\r\n      } else {\r\n        // 其他AI或联网搜索的正常切换逻辑\r\n        if (index === -1) {\r\n          // 如果不存在，则添加\r\n          this.$set(ai.selectedCapabilities, ai.selectedCapabilities.length, capabilityValue);\r\n        } else {\r\n          // 如果已存在，则移除\r\n          const newCapabilities = [...ai.selectedCapabilities];\r\n          newCapabilities.splice(index, 1);\r\n          this.$set(ai, 'selectedCapabilities', newCapabilities);\r\n        }\r\n      }\r\n\r\n      console.log('切换后:', ai.selectedCapabilities);\r\n      this.$forceUpdate(); // 强制更新视图\r\n    },\r\n    getStatusText(status) {\r\n      switch (status) {\r\n        case 'idle': return '等待中';\r\n        case 'running': return '正在执行';\r\n        case 'completed': return '已完成';\r\n        case 'failed': return '执行失败';\r\n        default: return '未知状态';\r\n      }\r\n    },\r\n    getStatusIcon(status) {\r\n      switch (status) {\r\n        case 'idle': return 'el-icon-time';\r\n        case 'running': return 'el-icon-loading';\r\n        case 'completed': return 'el-icon-check success-icon';\r\n        case 'failed': return 'el-icon-close error-icon';\r\n        default: return 'el-icon-question';\r\n      }\r\n    },\r\n    renderMarkdown(text) {\r\n      return marked(text);\r\n    },\r\n    // HTML转纯文本\r\n    htmlToText(html) {\r\n      const tempDiv = document.createElement('div');\r\n      tempDiv.innerHTML = html;\r\n      return tempDiv.textContent || tempDiv.innerText || '';\r\n    },\r\n\r\n    // HTML转Markdown\r\n    htmlToMarkdown(html) {\r\n      return this.turndownService.turndown(html);\r\n    },\r\n\r\n    copyResult(content) {\r\n      // 将HTML转换为纯文本\r\n      const plainText = this.htmlToText(content);\r\n      const textarea = document.createElement('textarea');\r\n      textarea.value = plainText;\r\n      document.body.appendChild(textarea);\r\n      textarea.select();\r\n      document.execCommand('copy');\r\n      document.body.removeChild(textarea);\r\n      this.$message.success('已复制纯文本到剪贴板');\r\n    },\r\n\r\n    exportResult(result) {\r\n      // 将HTML转换为Markdown\r\n      const markdown = result.content;\r\n      const blob = new Blob([markdown], { type: 'text/markdown' });\r\n      const link = document.createElement('a');\r\n      link.href = URL.createObjectURL(blob);\r\n      link.download = `${result.aiName}_结果_${new Date().toISOString().slice(0, 10)}.md`;\r\n      link.click();\r\n      URL.revokeObjectURL(link.href);\r\n      this.$message.success('已导出Markdown文件');\r\n    },\r\n\r\n    openShareUrl(shareUrl) {\r\n      if (shareUrl) {\r\n        window.open(shareUrl, '_blank');\r\n      } else {\r\n        this.$message.warning('暂无原链接');\r\n      }\r\n    },\r\n    showLargeImage(imageUrl) {\r\n      this.currentLargeImage = imageUrl;\r\n      this.showImageDialog = true;\r\n      // 找到当前图片的索引，设置轮播图的初始位置\r\n      const currentIndex = this.screenshots.indexOf(imageUrl);\r\n      if (currentIndex !== -1) {\r\n        this.$nextTick(() => {\r\n          const carousel = this.$el.querySelector('.image-dialog .el-carousel');\r\n          if (carousel && carousel.__vue__) {\r\n            carousel.__vue__.setActiveItem(currentIndex);\r\n          }\r\n        });\r\n      }\r\n    },\r\n    closeLargeImage() {\r\n      this.showImageDialog = false;\r\n      this.currentLargeImage = '';\r\n    },\r\n    // WebSocket 相关方法\r\n    initWebSocket(id) {\r\n      const wsUrl = process.env.VUE_APP_WS_API + `mypc-${id}`;\r\n      console.log('WebSocket URL:', process.env.VUE_APP_WS_API);\r\n      websocketClient.connect(wsUrl, (event) => {\r\n        switch (event.type) {\r\n          case 'open':\r\n            // this.$message.success('');\r\n            break;\r\n          case 'message':\r\n            this.handleWebSocketMessage(event.data);\r\n            break;\r\n          case 'close':\r\n            this.$message.warning('WebSocket连接已关闭');\r\n            break;\r\n          case 'error':\r\n            this.$message.error('WebSocket连接错误');\r\n            break;\r\n          case 'reconnect_failed':\r\n            this.$message.error('WebSocket重连失败，请刷新页面重试');\r\n            break;\r\n        }\r\n      });\r\n    },\r\n\r\n    handleWebSocketMessage(data) {\r\n\r\n      const datastr = data;\r\n      const dataObj = JSON.parse(datastr);\r\n\r\n      // 处理chatId消息\r\n      if (dataObj.type === 'RETURN_YBT1_CHATID' && dataObj.chatId) {\r\n        this.userInfoReq.toneChatId = dataObj.chatId;\r\n      } else if (dataObj.type === 'RETURN_YBDS_CHATID' && dataObj.chatId) {\r\n        this.userInfoReq.ybDsChatId = dataObj.chatId;\r\n      } else if (dataObj.type === 'RETURN_DB_CHATID' && dataObj.chatId) {\r\n        this.userInfoReq.dbChatId = dataObj.chatId;\r\n      }\r\n\r\n      // 处理进度日志消息\r\n      if (dataObj.type === 'RETURN_PC_TASK_LOG' && dataObj.aiName) {\r\n        const targetAI = this.enabledAIs.find(ai => ai.name === dataObj.aiName);\r\n        if (targetAI) {\r\n          // 将新进度添加到数组开头\r\n          targetAI.progressLogs.unshift({\r\n            content: dataObj.content,\r\n            timestamp: new Date(),\r\n            isCompleted: false\r\n          });\r\n        }\r\n        return;\r\n      }\r\n\r\n      // 处理截图消息\r\n      if (dataObj.type === 'RETURN_PC_TASK_IMG' && dataObj.url) {\r\n        // 将新的截图添加到数组开头\r\n        this.screenshots.unshift(dataObj.url);\r\n        return;\r\n      }\r\n\r\n              // 处理智能评分结果\r\n      if (dataObj.type === 'RETURN_WKPF_RES') {\r\n        const wkpfAI = this.enabledAIs.find(ai => ai.name === '智能评分');\r\n        if (wkpfAI) {\r\n          this.$set(wkpfAI, 'status', 'completed');\r\n          if (wkpfAI.progressLogs.length > 0) {\r\n            this.$set(wkpfAI.progressLogs[0], 'isCompleted', true);\r\n          }\r\n          // 添加评分结果到results最前面\r\n          this.results.unshift({\r\n            aiName: '智能评分',\r\n            content: dataObj.draftContent,\r\n            shareUrl: dataObj.shareUrl || '',\r\n            shareImgUrl: dataObj.shareImgUrl || '',\r\n            timestamp: new Date()\r\n          });\r\n          this.activeResultTab = 'result-0';\r\n\r\n          // 智能评分完成时，再次保存历史记录\r\n          this.saveHistory();\r\n        }\r\n        return;\r\n      }\r\n\r\n      // 根据消息类型更新对应AI的状态和结果\r\n      let targetAI = null;\r\n      switch (dataObj.type) {\r\n        case 'RETURN_YBT1_RES':\r\n          console.log('收到消息:', data);\r\n          targetAI = this.enabledAIs.find(ai => ai.name === '腾讯元宝T1');\r\n          break;\r\n        case 'RETURN_YBDS_RES':\r\n          console.log('收到消息:', data);\r\n          targetAI = this.enabledAIs.find(ai => ai.name === '腾讯元宝DS');\r\n          break;\r\n        case 'RETURN_DB_RES':\r\n          console.log('收到消息:', data);\r\n          targetAI = this.enabledAIs.find(ai => ai.name === '豆包');\r\n          break;\r\n        case 'RETURN_WX_RES':\r\n          console.log('收到消息:', data);\r\n          targetAI = this.enabledAIs.find(ai => ai.name === '百度AI');\r\n          break;\r\n        case 'RETURN_TURBOS_RES':\r\n          console.log('收到消息:', data);\r\n          targetAI = this.enabledAIs.find(ai => ai.name === 'TurboS@元器');\r\n          break;\r\n        case 'RETURN_TURBOS_LARGE_RES':\r\n          console.log('收到消息:', data);\r\n          targetAI = this.enabledAIs.find(ai => ai.name === 'TurboS长文版@元器');\r\n          break;\r\n        // case 'RETURN_MINI_MAX_RES':\r\n        //   targetAI = this.enabledAIs.find(ai => ai.name === 'MiniMax@元器');\r\n        //   break;\r\n      }\r\n\r\n      if (targetAI) {\r\n        // 更新AI状态为已完成\r\n        this.$set(targetAI, 'status', 'completed');\r\n\r\n        // 将最后一条进度消息标记为已完成\r\n        if (targetAI.progressLogs.length > 0) {\r\n          this.$set(targetAI.progressLogs[0], 'isCompleted', true);\r\n        }\r\n\r\n        // 添加结果到数组开头\r\n        const resultIndex = this.results.findIndex(r => r.aiName === targetAI.name);\r\n        if (resultIndex === -1) {\r\n          this.results.unshift({\r\n            aiName: targetAI.name,\r\n            content: dataObj.draftContent,\r\n            shareUrl: dataObj.shareUrl || '',\r\n            shareImgUrl: dataObj.shareImgUrl || '',\r\n            timestamp: new Date()\r\n          });\r\n          this.activeResultTab = 'result-0';\r\n        } else {\r\n          this.results.splice(resultIndex, 1);\r\n          this.results.unshift({\r\n            aiName: targetAI.name,\r\n            content: dataObj.draftContent,\r\n            shareUrl: dataObj.shareUrl || '',\r\n            shareImgUrl: dataObj.shareImgUrl || '',\r\n            timestamp: new Date()\r\n          });\r\n          this.activeResultTab = 'result-0';\r\n        }\r\n        this.saveHistory();\r\n      }\r\n\r\n      // 检查是否所有任务都已完成\r\n      // const allCompleted = this.enabledAIs.every(ai =>\r\n      //   ai.status === 'completed' || ai.status === 'failed'\r\n      // );\r\n\r\n      // if (allCompleted) {\r\n      //\r\n      // }\r\n    },\r\n\r\n    closeWebSocket() {\r\n      websocketClient.close();\r\n    },\r\n\r\n    sendMessage(data) {\r\n      if (websocketClient.send(data)) {\r\n        // 滚动到底部\r\n        this.$nextTick(() => {\r\n          this.scrollToBottom();\r\n        });\r\n      } else {\r\n        this.$message.error('WebSocket未连接');\r\n      }\r\n    },\r\n    toggleAIExpansion(ai) {\r\n      this.$set(ai, 'isExpanded', !ai.isExpanded);\r\n    },\r\n\r\n    formatTime(timestamp) {\r\n      const date = new Date(timestamp);\r\n      return date.toLocaleTimeString('zh-CN', {\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n        second: '2-digit',\r\n        hour12: false\r\n      });\r\n    },\r\n    showScoreDialog() {\r\n      this.scoreDialogVisible = true;\r\n      this.selectedResults = [];\r\n    },\r\n\r\n    handleScore() {\r\n      if (!this.canScore) return;\r\n\r\n      // 获取选中的结果内容并按照指定格式拼接\r\n      const selectedContents = this.results\r\n        .filter(result => this.selectedResults.includes(result.aiName))\r\n        .map(result => {\r\n          // 将HTML内容转换为纯文本\r\n          const plainContent = this.htmlToText(result.content);\r\n          return `${result.aiName}初稿：\\n${plainContent}\\n`;\r\n        })\r\n        .join('\\n');\r\n\r\n      // 构建完整的评分提示内容\r\n      const fullPrompt = `${this.scorePrompt}\\n${selectedContents}`;\r\n\r\n      // 构建评分请求\r\n      const scoreRequest = {\r\n        jsonrpc: '2.0',\r\n        id: uuidv4(),\r\n        method: 'AI评分',\r\n        params: {\r\n          taskId: uuidv4(),\r\n          userId: this.userId,\r\n          corpId: this.corpId,\r\n          userPrompt: fullPrompt,\r\n          roles: 'zj-db-sdsk' // 默认使用豆包进行评分\r\n        }\r\n      };\r\n\r\n      // 发送评分请求\r\n      console.log(\"参数\", scoreRequest)\r\n      this.message(scoreRequest);\r\n      this.scoreDialogVisible = false;\r\n\r\n      // 创建智能评分AI节点\r\n      const wkpfAI = {\r\n        name: '智能评分',\r\n        avatar: require('../../../assets/ai/yuanbao.png'),\r\n        capabilities: [\r\n          { label: 'DeepSeek-R1最新版', value: 'deepseek-r1' },\r\n          { label: 'DeepSeek-V3最新版', value: 'deepseek-v3' }\r\n        ],\r\n        selectedCapabilities: ['deepseek-r1'],\r\n        enabled: true,\r\n        status: 'running',\r\n        progressLogs: [\r\n          {\r\n            content: '智能评分任务已提交，正在评分...',\r\n            timestamp: new Date(),\r\n            isCompleted: false,\r\n            type: '智能评分'\r\n          }\r\n        ],\r\n        isExpanded: true\r\n      };\r\n\r\n      // 检查是否已存在智能评分\r\n      const existIndex = this.enabledAIs.findIndex(ai => ai.name === '智能评分');\r\n      if (existIndex === -1) {\r\n        // 如果不存在，添加到数组开头\r\n        this.enabledAIs.unshift(wkpfAI);\r\n      } else {\r\n        // 如果已存在，更新状态和日志\r\n        this.enabledAIs[existIndex] = wkpfAI;\r\n        // 将智能评分移到数组开头\r\n        const wkpf = this.enabledAIs.splice(existIndex, 1)[0];\r\n        this.enabledAIs.unshift(wkpf);\r\n      }\r\n\r\n      this.$forceUpdate();\r\n      this.$message.success('评分请求已发送，请等待结果');\r\n    },\r\n    // 显示历史记录抽屉\r\n    showHistoryDrawer() {\r\n      this.historyDrawerVisible = true;\r\n      this.loadChatHistory(1);\r\n    },\r\n\r\n    // 关闭历史记录抽屉\r\n    handleHistoryDrawerClose() {\r\n      this.historyDrawerVisible = false;\r\n    },\r\n\r\n    // 加载历史记录\r\n    async loadChatHistory(isAll) {\r\n      try {\r\n        const res = await getChatHistory(this.userId, isAll);\r\n        if (res.code === 200) {\r\n          this.chatHistory = res.data || [];\r\n        }\r\n      } catch (error) {\r\n        console.error('加载历史记录失败:', error);\r\n        this.$message.error('加载历史记录失败');\r\n      }\r\n    },\r\n\r\n    // 格式化历史记录时间\r\n    formatHistoryTime(timestamp) {\r\n      const date = new Date(timestamp);\r\n      return date.toLocaleTimeString('zh-CN', {\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n        hour12: false\r\n      });\r\n    },\r\n\r\n    // 获取历史记录日期分组\r\n    getHistoryDate(timestamp) {\r\n      const date = new Date(timestamp);\r\n      const today = new Date();\r\n      const yesterday = new Date(today);\r\n      yesterday.setDate(yesterday.getDate() - 1);\r\n      const twoDaysAgo = new Date(today);\r\n      twoDaysAgo.setDate(twoDaysAgo.getDate() - 2);\r\n      const threeDaysAgo = new Date(today);\r\n      threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);\r\n\r\n      if (date.toDateString() === today.toDateString()) {\r\n        return '今天';\r\n      } else if (date.toDateString() === yesterday.toDateString()) {\r\n        return '昨天';\r\n      } else if (date.toDateString() === twoDaysAgo.toDateString()) {\r\n        return '两天前';\r\n      } else if (date.toDateString() === threeDaysAgo.toDateString()) {\r\n        return '三天前';\r\n      } else {\r\n        return date.toLocaleDateString('zh-CN', {\r\n          year: 'numeric',\r\n          month: 'long',\r\n          day: 'numeric'\r\n        });\r\n      }\r\n    },\r\n\r\n    // 加载历史记录项\r\n    loadHistoryItem(item) {\r\n      try {\r\n        const historyData = JSON.parse(item.data);\r\n        // 恢复AI选择配置\r\n        this.aiList = historyData.aiList || this.aiList;\r\n        // 恢复提示词输入\r\n        this.promptInput = historyData.promptInput || '';\r\n        // 恢复任务流程\r\n        this.enabledAIs = historyData.enabledAIs || [];\r\n        // 恢复主机可视化\r\n        this.screenshots = historyData.screenshots || [];\r\n        // 恢复执行结果\r\n        this.results = historyData.results || [];\r\n        // 恢复chatId\r\n        this.chatId = item.chatId || this.chatId;\r\n        this.userInfoReq.toneChatId = item.toneChatId || '';\r\n        this.userInfoReq.ybDsChatId = item.ybDsChatId || '';\r\n        this.userInfoReq.dbChatId = item.dbChatId || '';\r\n        this.userInfoReq.isNewChat = false;\r\n\r\n        // 展开相关区域\r\n        this.activeCollapses = ['ai-selection', 'prompt-input'];\r\n        this.taskStarted = true;\r\n\r\n        this.$message.success('历史记录加载成功');\r\n        this.historyDrawerVisible = false;\r\n      } catch (error) {\r\n        console.error('加载历史记录失败:', error);\r\n        this.$message.error('加载历史记录失败');\r\n      }\r\n    },\r\n\r\n    // 保存历史记录\r\n    async saveHistory() {\r\n      // if (!this.taskStarted || this.enabledAIs.some(ai => ai.status === 'running')) {\r\n      //   return;\r\n      // }\r\n\r\n      const historyData = {\r\n        aiList: this.aiList,\r\n        promptInput: this.promptInput,\r\n        enabledAIs: this.enabledAIs,\r\n        screenshots: this.screenshots,\r\n        results: this.results,\r\n        chatId: this.chatId,\r\n        toneChatId: this.userInfoReq.toneChatId,\r\n        ybDsChatId: this.userInfoReq.ybDsChatId,\r\n        dbChatId: this.userInfoReq.dbChatId\r\n      };\r\n\r\n      try {\r\n        await saveUserChatData({\r\n          userId: this.userId,\r\n          userPrompt: this.promptInput,\r\n          data: JSON.stringify(historyData),\r\n          chatId: this.chatId,\r\n          toneChatId: this.userInfoReq.toneChatId,\r\n          ybDsChatId: this.userInfoReq.ybDsChatId,\r\n          dbChatId: this.userInfoReq.dbChatId\r\n        });\r\n      } catch (error) {\r\n        console.error('保存历史记录失败:', error);\r\n        this.$message.error('保存历史记录失败');\r\n      }\r\n    },\r\n\r\n    // 修改折叠切换方法\r\n    toggleHistoryExpansion(item) {\r\n      this.$set(this.expandedHistoryItems, item.chatId, !this.expandedHistoryItems[item.chatId]);\r\n    },\r\n\r\n    // 创建新对话\r\n    createNewChat() {\r\n      // 重置所有数据\r\n      this.chatId = uuidv4();\r\n      this.isNewChat = true;\r\n      this.promptInput = '';\r\n      this.taskStarted = false;\r\n      this.screenshots = [];\r\n      this.results = [];\r\n      this.enabledAIs = [];\r\n      this.userInfoReq = {\r\n        userPrompt: '',\r\n        userId: this.userId,\r\n        corpId: this.corpId,\r\n        taskId: '',\r\n        roles: '',\r\n        toneChatId: '',\r\n        ybDsChatId: '',\r\n        dbChatId: '',\r\n        isNewChat: true\r\n      };\r\n      // 重置AI列表为初始状态\r\n      this.aiList = [\r\n        {\r\n          name: 'TurboS@元器',\r\n          avatar: require('../../../assets/ai/yuanbao.png'),\r\n          capabilities: [],\r\n          selectedCapabilities: [],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        },\r\n        {\r\n          name: 'TurboS长文版@元器',\r\n          avatar: require('../../../assets/ai/yuanbao.png'),\r\n          capabilities: [],\r\n          selectedCapabilities: [],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        },\r\n        // {\r\n        //   name: 'MiniMax@元器',\r\n        //   avatar: require('../../../assets/ai/yuanbao.png'),\r\n        //   capabilities: [],\r\n        //   selectedCapabilities: [],\r\n        //   enabled: true,\r\n        //   status: 'idle',\r\n        //   progressLogs: [],\r\n        //   isExpanded: true\r\n        // },\r\n        // {\r\n        //   name: 'KIMI@元器',\r\n        //   avatar: require('../../../assets/ai/yuanbao.png'),\r\n        //   capabilities: [],\r\n        //   selectedCapabilities: [],\r\n        //   enabled: true,\r\n        //   status: 'idle',\r\n        //   progressLogs: [],\r\n        //   isExpanded: true\r\n        // },\r\n        {\r\n          name: '腾讯元宝T1',\r\n          avatar: require('../../../assets/ai/yuanbao.png'),\r\n          capabilities: [\r\n            { label: '深度思考', value: 'deep_thinking' },\r\n            { label: '联网搜索', value: 'web_search' }\r\n          ],\r\n          selectedCapabilities: ['deep_thinking','web_search'],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        },\r\n        {\r\n          name: '腾讯元宝DS',\r\n          avatar: require('../../../assets/ai/yuanbao.png'),\r\n          capabilities: [\r\n            { label: '深度思考', value: 'deep_thinking' },\r\n            { label: '联网搜索', value: 'web_search' }\r\n          ],\r\n          selectedCapabilities: ['deep_thinking','web_search'],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        },\r\n        {\r\n          name: '豆包',\r\n          avatar: require('../../../assets/ai/豆包.png'),\r\n          capabilities: [\r\n            { label: '深度思考', value: 'deep_thinking' }\r\n          ],\r\n          selectedCapabilities: ['deep_thinking'],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        },\r\n        {\r\n          name: '百度AI',\r\n          avatar: require('../../../assets/ai/baidu.png'),\r\n          capabilities: [\r\n            { label: 'DeepSeek-R1最新版', value: 'deepseek-r1' },\r\n            { label: 'DeepSeek-V3最新版', value: 'deepseek-v3' },\r\n            { label: '联网搜索', value: 'internet-search' }\r\n          ],\r\n          selectedCapabilities: ['deepseek-r1'],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        }\r\n      ];\r\n      // 展开相关区域\r\n      this.activeCollapses = ['ai-selection', 'prompt-input'];\r\n\r\n      this.$message.success('已创建新对话');\r\n    },\r\n\r\n    // 加载上次会话\r\n    async loadLastChat() {\r\n      try {\r\n        const res = await getChatHistory(this.userId,0);\r\n        if (res.code === 200 && res.data && res.data.length > 0) {\r\n          // 获取最新的会话记录\r\n          const lastChat = res.data[0];\r\n          this.loadHistoryItem(lastChat);\r\n        }\r\n      } catch (error) {\r\n        console.error('加载上次会话失败:', error);\r\n      }\r\n    },\r\n\r\n    // 判断是否为图片文件\r\n    isImageFile(url) {\r\n      if (!url) return false;\r\n      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'];\r\n      const urlLower = url.toLowerCase();\r\n      return imageExtensions.some(ext => urlLower.includes(ext));\r\n    },\r\n\r\n    // 判断是否为PDF文件\r\n    isPdfFile(url) {\r\n      if (!url) return false;\r\n      return url.toLowerCase().includes('.pdf');\r\n    },\r\n\r\n    // 根据AI名称获取图片样式\r\n    getImageStyle(aiName) {\r\n      const widthMap = {\r\n        'TurboS@元器': '700px',\r\n        '腾讯元宝DS': '700px',\r\n        'TurboS长文版@元器': '700px',\r\n        '腾讯元宝T1': '700px',\r\n        '豆包': '560px',\r\n        '百度AI': '700px'\r\n      };\r\n\r\n      const width = widthMap[aiName] || '560px'; // 默认宽度\r\n\r\n      return {\r\n        width: width,\r\n        height: 'auto'\r\n      };\r\n    },\r\n\r\n    // 投递到公众号\r\n    handlePushToWechat(result) {\r\n      if (this.pushingToWechat) return; // 防止重复点击\r\n\r\n      this.pushingToWechat = true; // 开始loading\r\n      this.pushOfficeNum += 1; // 递增编号\r\n\r\n      const params = {\r\n        contentText: result.content,\r\n        shareUrl: result.shareUrl,\r\n        userId: this.userId,\r\n        num: this.pushOfficeNum,\r\n        aiName: result.aiName\r\n      };\r\n\r\n      pushAutoOffice(params).then(res => {\r\n        if (res.code === 200) {\r\n          this.$message.success('投递到公众号成功！');\r\n        } else {\r\n          this.$message.error(res.msg || '投递失败，请重试');\r\n        }\r\n      }).catch(error => {\r\n        console.error('投递到公众号失败:', error);\r\n        this.$message.error('投递失败，请重试');\r\n      }).finally(() => {\r\n        this.pushingToWechat = false; // 结束loading\r\n      });\r\n    },\r\n\r\n\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.ai-management-platform {\r\n  min-height: 100vh;\r\n  background-color: #f5f7fa;\r\n  padding-bottom: 30px;\r\n}\r\n\r\n.top-nav {\r\n  background-color: #fff;\r\n  padding: 15px 20px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n  margin-bottom: 20px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.logo-area {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.logo {\r\n  height: 36px;\r\n  margin-right: 10px;\r\n}\r\n\r\n.platform-title {\r\n  margin: 0;\r\n  font-size: 20px;\r\n  color: #303133;\r\n}\r\n\r\n.main-content {\r\n  padding: 0 30px;\r\n  width: 90%;\r\n  margin: 0 auto;\r\n}\r\n::v-deep .el-collapse-item__header {\r\n  font-size: 16px;\r\n  color: #333;\r\n  padding-left: 20px;\r\n}\r\n.section-title {\r\n  font-size: 18px;\r\n  color: #606266;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.ai-cards {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 20px;\r\n  margin-bottom: 0px;\r\n  margin-left: 20px;\r\n  margin-top: 10px;\r\n}\r\n\r\n.ai-card {\r\n  width: calc(25% - 20px);\r\n  box-sizing: border-box;\r\n}\r\n\r\n.ai-card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.ai-left {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.ai-avatar {\r\n  margin-right: 10px;\r\n}\r\n\r\n.ai-avatar img {\r\n  width: 30px;\r\n  height: 30px;\r\n  border-radius: 50%;\r\n  object-fit: cover;\r\n}\r\n\r\n.ai-name {\r\n  font-weight: bold;\r\n  font-size: 12px;\r\n}\r\n\r\n.ai-status {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.ai-capabilities {\r\n  margin: 15px 0;\r\n  width: 100%;\r\n  display: flex;\r\n  justify-content: center;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.button-capability-group {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  justify-content: center;\r\n  gap: 8px;\r\n}\r\n\r\n.button-capability-group .el-button {\r\n  margin: 0;\r\n  border-radius: 16px;\r\n  padding: 6px 12px;\r\n}\r\n\r\n.button-capability-group .el-button.is-plain:hover,\r\n.button-capability-group .el-button.is-plain:focus {\r\n  background: #ecf5ff;\r\n  border-color: #b3d8ff;\r\n  color: #409EFF;\r\n}\r\n\r\n.prompt-input-section {\r\n  margin-bottom: 30px;\r\n  padding: 0 20px 0 0px;\r\n}\r\n\r\n.prompt-input {\r\n  margin-bottom: 10px;\r\n  margin-left: 20px;\r\n  width: 99%;\r\n}\r\n\r\n.prompt-footer {\r\n  display: flex;\r\n  margin-bottom: -30px;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.word-count {\r\n  font-size: 12px;\r\n  padding-left: 20px;\r\n}\r\n\r\n.send-button {\r\n  padding: 10px 20px;\r\n}\r\n\r\n.execution-status-section {\r\n  margin-bottom: 30px;\r\n  padding:20px 0px 0px 0px;\r\n}\r\n\r\n.task-flow-card, .screenshots-card {\r\n  height: 800px;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.task-flow {\r\n  padding: 15px;\r\n  height: 800px;\r\n  overflow-y: auto;\r\n  background-color: #f5f7fa;\r\n  border-radius: 4px;\r\n}\r\n\r\n.task-flow::-webkit-scrollbar {\r\n  width: 6px;\r\n}\r\n\r\n.task-flow::-webkit-scrollbar-thumb {\r\n  background-color: #c0c4cc;\r\n  border-radius: 3px;\r\n}\r\n\r\n.task-flow::-webkit-scrollbar-track {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.task-item {\r\n  margin-bottom: 15px;\r\n  border-radius: 4px;\r\n  background-color: #fff;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n}\r\n\r\n.task-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 12px 15px;\r\n  cursor: pointer;\r\n  transition: background-color 0.3s;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.task-header:hover {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.header-left {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.header-left .el-icon-arrow-right {\r\n  transition: transform 0.3s;\r\n  font-size: 14px;\r\n  color: #909399;\r\n}\r\n\r\n.header-left .el-icon-arrow-right.is-expanded {\r\n  transform: rotate(90deg);\r\n}\r\n\r\n.progress-timeline {\r\n  position: relative;\r\n  margin: 0;\r\n  padding: 15px 0;\r\n}\r\n\r\n.timeline-scroll {\r\n  max-height: 200px;\r\n  overflow-y: auto;\r\n  padding: 0 15px;\r\n}\r\n\r\n.timeline-scroll::-webkit-scrollbar {\r\n  width: 4px;\r\n}\r\n\r\n.timeline-scroll::-webkit-scrollbar-thumb {\r\n  background-color: #c0c4cc;\r\n  border-radius: 2px;\r\n}\r\n\r\n.timeline-scroll::-webkit-scrollbar-track {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.progress-item {\r\n  position: relative;\r\n  padding: 8px 0 8px 20px;\r\n  display: flex;\r\n  align-items: flex-start;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.progress-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.progress-dot {\r\n  position: absolute;\r\n  left: 0;\r\n  top: 12px;\r\n  width: 10px;\r\n  height: 10px;\r\n  border-radius: 50%;\r\n  background-color: #e0e0e0;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.progress-line {\r\n  position: absolute;\r\n  left: 4px;\r\n  top: 22px;\r\n  bottom: -8px;\r\n  width: 2px;\r\n  background-color: #e0e0e0;\r\n}\r\n\r\n.progress-content {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n\r\n.progress-time {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.progress-text {\r\n  font-size: 13px;\r\n  color: #606266;\r\n  line-height: 1.4;\r\n  word-break: break-all;\r\n}\r\n\r\n.progress-item.completed .progress-dot {\r\n  background-color: #67c23a;\r\n}\r\n\r\n.progress-item.completed .progress-line {\r\n  background-color: #67c23a;\r\n}\r\n\r\n.progress-item.current .progress-dot {\r\n  background-color: #409eff;\r\n  animation: pulse 1.5s infinite;\r\n}\r\n\r\n.progress-item.current .progress-line {\r\n  background-color: #409eff;\r\n}\r\n\r\n.ai-name {\r\n  font-weight: 600;\r\n  font-size: 14px;\r\n  color: #303133;\r\n}\r\n\r\n.header-right {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.status-text {\r\n  font-size: 13px;\r\n  color: #606266;\r\n}\r\n\r\n.status-icon {\r\n  font-size: 16px;\r\n}\r\n\r\n.success-icon {\r\n  color: #67c23a;\r\n}\r\n\r\n.error-icon {\r\n  color: #f56c6c;\r\n}\r\n\r\n@keyframes pulse {\r\n  0% {\r\n    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0.4);\r\n  }\r\n  70% {\r\n    box-shadow: 0 0 0 6px rgba(64, 158, 255, 0);\r\n  }\r\n  100% {\r\n    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0);\r\n  }\r\n}\r\n\r\n.screenshot-image {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: contain;\r\n  cursor: pointer;\r\n  transition: transform 0.3s;\r\n}\r\n\r\n.screenshot-image:hover {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.results-section {\r\n  margin-top: 20px;\r\n  padding: 0 10px;\r\n}\r\n\r\n.result-content {\r\n  padding: 20px 30px;\r\n}\r\n\r\n.result-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.result-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n}\r\n\r\n.result-buttons {\r\n  display: flex;\r\n  gap: 10px;\r\n  align-items: center;\r\n}\r\n\r\n.share-link-btn, .push-wechat-btn {\r\n  border-radius: 16px;\r\n  padding: 6px 12px;\r\n}\r\n\r\n.markdown-content {\r\n  margin-bottom: 20px;\r\n  max-height: 400px;\r\n  overflow-y: auto;\r\n  padding: 15px 20px;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n  background-color: #fff;\r\n}\r\n\r\n.action-buttons {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  gap: 10px;\r\n  padding: 0 10px;\r\n}\r\n\r\n@media (max-width: 1200px) {\r\n  .ai-card {\r\n    width: calc(33.33% - 14px);\r\n  }\r\n}\r\n\r\n@media (max-width: 992px) {\r\n  .ai-card {\r\n    width: calc(50% - 10px);\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .ai-card {\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n.el-collapse {\r\n  border-top: none;\r\n  border-bottom: none;\r\n}\r\n\r\n\r\n\r\n.el-collapse-item__content {\r\n  padding: 15px 0;\r\n}\r\n\r\n.ai-selection-section {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.prompt-input-section {\r\n  margin-bottom: 30px;\r\n  padding: 0 20px 0 0px;\r\n}\r\n\r\n.image-dialog .el-dialog__body {\r\n  padding: 0;\r\n}\r\n\r\n.large-image-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  background-color: #000;\r\n}\r\n\r\n.large-image {\r\n  max-width: 100%;\r\n  max-height: 80vh;\r\n  object-fit: contain;\r\n}\r\n\r\n.image-dialog .el-carousel {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.image-dialog .el-carousel__container {\r\n  height: 80vh;\r\n}\r\n\r\n.image-dialog .el-carousel__item {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  background-color: #000;\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.score-dialog-content {\r\n  padding: 20px;\r\n}\r\n\r\n.selected-results {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.result-checkbox {\r\n  margin-right: 20px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.score-prompt-section {\r\n  margin-top: 20px;\r\n}\r\n\r\n.score-prompt-input {\r\n  margin-top: 10px;\r\n}\r\n\r\n.score-prompt-input .el-textarea__inner {\r\n  min-height: 500px !important;\r\n}\r\n\r\n.dialog-footer {\r\n  text-align: right;\r\n}\r\n\r\n.score-dialog .el-dialog {\r\n  height: 95vh;\r\n  margin-top: 2.5vh !important;\r\n}\r\n\r\n.score-dialog .el-dialog__body {\r\n  height: calc(95vh - 120px);\r\n  overflow-y: auto;\r\n  padding: 20px;\r\n}\r\n\r\n.nav-buttons {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 20px;\r\n}\r\n\r\n.history-button {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.history-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n  vertical-align: middle;\r\n}\r\n\r\n.history-content {\r\n  padding: 20px;\r\n}\r\n\r\n.history-group {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.history-date {\r\n  font-size: 14px;\r\n  color: #909399;\r\n  margin-bottom: 10px;\r\n  padding: 5px 0;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.history-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 10px;\r\n}\r\n\r\n.history-item {\r\n  margin-bottom: 15px;\r\n  border-radius: 4px;\r\n  background-color: #f5f7fa;\r\n  overflow: hidden;\r\n}\r\n\r\n.history-parent {\r\n  padding: 10px;\r\n  cursor: pointer;\r\n  transition: background-color 0.3s;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.history-parent:hover {\r\n  background-color: #ecf5ff;\r\n}\r\n\r\n.history-children {\r\n  padding-left: 20px;\r\n  background-color: #fff;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.history-child-item {\r\n  padding: 8px 10px;\r\n  cursor: pointer;\r\n  transition: background-color 0.3s;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.history-child-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.history-child-item:hover {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.history-header {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  gap: 8px;\r\n}\r\n\r\n.history-header .el-icon-arrow-right {\r\n  font-size: 14px;\r\n  color: #909399;\r\n  transition: transform 0.3s;\r\n  cursor: pointer;\r\n  margin-top: 3px;\r\n}\r\n\r\n.history-header .el-icon-arrow-right.is-expanded {\r\n  transform: rotate(90deg);\r\n}\r\n\r\n.history-prompt {\r\n  font-size: 14px;\r\n  color: #303133;\r\n  margin-bottom: 5px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n  flex: 1;\r\n}\r\n\r\n.history-time {\r\n  font-size: 12px;\r\n  color: #909399;\r\n}\r\n\r\n.capability-button {\r\n  transition: all 0.3s;\r\n}\r\n\r\n.capability-button.el-button--primary {\r\n  background-color: #409EFF;\r\n  border-color: #409EFF;\r\n  color: #fff;\r\n}\r\n\r\n.capability-button.el-button--info {\r\n  background-color: #fff;\r\n  border-color: #dcdfe6;\r\n  color: #606266;\r\n}\r\n\r\n.capability-button.el-button--info:hover {\r\n  color: #409EFF;\r\n  border-color: #c6e2ff;\r\n  background-color: #ecf5ff;\r\n}\r\n\r\n.capability-button.el-button--primary:hover {\r\n  background-color: #66b1ff;\r\n  border-color: #66b1ff;\r\n  color: #fff;\r\n}\r\n\r\n/* 分享内容样式 */\r\n.share-content {\r\n  margin-bottom: 20px;\r\n  padding: 15px 20px;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n  background-color: #fff;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: flex-start;\r\n  min-height: 600px;\r\n  max-height: 800px;\r\n  overflow: auto;\r\n}\r\n\r\n.share-image {\r\n  object-fit: contain;\r\n  display: block;\r\n}\r\n\r\n.share-pdf {\r\n  width: 100%;\r\n  height: 600px;\r\n  border: none;\r\n  border-radius: 4px;\r\n}\r\n\r\n.share-file {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 200px;\r\n  flex-direction: column;\r\n  color: #909399;\r\n}\r\n\r\n.single-image-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  width: 100%;\r\n  height: 80vh;\r\n}\r\n\r\n.single-image-container .large-image {\r\n  max-width: 100%;\r\n  max-height: 100%;\r\n  object-fit: contain;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoQA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AAGA,IAAAG,UAAA,GAAAC,sBAAA,CAAAJ,OAAA;AACA,IAAAK,MAAA,GAAAD,sBAAA,CAAAJ,OAAA;AACA,IAAAM,SAAA,GAAAF,sBAAA,CAAAJ,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAO,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,MAAA,EAAAC,cAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,EAAA;MACAC,MAAA,EAAAJ,cAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAG,OAAA;MACAC,MAAA,MAAAC,QAAA;MACAC,oBAAA;MACAC,WAAA;QACAC,UAAA;QACAX,MAAA;QACAK,MAAA;QACAO,MAAA;QACAC,KAAA;QACAC,UAAA;QACAC,UAAA;QACAC,QAAA;QACAC,SAAA;MACA;MACAC,aAAA;QACAC,OAAA;QACAf,EAAA,MAAAI,QAAA;QACAY,MAAA;QACAC,MAAA;MACA;MACAC,MAAA,GACA;QACAxB,IAAA;QACAyB,MAAA,EAAAhC,OAAA;QACAiC,YAAA;QACAC,oBAAA;QACAC,OAAA;QACAC,MAAA;QACAC,YAAA;QACAC,UAAA;MACA,GACA;QACA/B,IAAA;QACAyB,MAAA,EAAAhC,OAAA;QACAiC,YAAA;QACAC,oBAAA;QACAC,OAAA;QACAC,MAAA;QACAC,YAAA;QACAC,UAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA/B,IAAA;QACAyB,MAAA,EAAAhC,OAAA;QACAiC,YAAA,GACA;UAAAM,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,EACA;QACAN,oBAAA;QACAC,OAAA;QACAC,MAAA;QACAC,YAAA;QACAC,UAAA;MACA,GACA;QACA/B,IAAA;QACAyB,MAAA,EAAAhC,OAAA;QACAiC,YAAA,GACA;UAAAM,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,EACA;QACAN,oBAAA;QACAC,OAAA;QACAC,MAAA;QACAC,YAAA;QACAC,UAAA;MACA,GACA;QACA/B,IAAA;QACAyB,MAAA,EAAAhC,OAAA;QACAiC,YAAA,GACA;UAAAM,KAAA;UAAAC,KAAA;QAAA,EACA;QACAN,oBAAA;QACAC,OAAA;QACAC,MAAA;QACAC,YAAA;QACAC,UAAA;MACA,GACA;QACA/B,IAAA;QACAyB,MAAA,EAAAhC,OAAA;QACAiC,YAAA;QACAC,oBAAA;QACAC,OAAA;QACAC,MAAA;QACAC,YAAA;QACAC,UAAA;MACA,EACA;MACAG,WAAA;MACAC,WAAA;MACAC,QAAA;MACAC,WAAA;MACAC,OAAA;MACAC,eAAA;MACAC,eAAA;MAAA;MACAC,eAAA;MACAC,iBAAA;MACAC,UAAA;MACAC,eAAA,MAAAC,iBAAA;QACAC,YAAA;QACAC,cAAA;QACAC,WAAA;MACA;MACAC,kBAAA;MACAC,eAAA;MACAC,WAAA;MACAC,oBAAA;MACAC,WAAA;MACAC,aAAA;MAAA;MACAC,eAAA;IACA;EACA;EACAC,QAAA;IACAC,OAAA,WAAAA,QAAA;MACA,YAAAvB,WAAA,CAAAwB,IAAA,GAAAC,MAAA,aAAAnC,MAAA,CAAAoC,IAAA,WAAAC,EAAA;QAAA,OAAAA,EAAA,CAAAjC,OAAA;MAAA;IACA;IACAkC,QAAA,WAAAA,SAAA;MACA,YAAAZ,eAAA,CAAAS,MAAA,aAAAR,WAAA,CAAAO,IAAA,GAAAC,MAAA;IACA;IACAI,cAAA,WAAAA,eAAA;MAAA,IAAAC,KAAA;MACA,IAAAC,MAAA;MACA,IAAAC,UAAA;;MAEA;MACA,KAAAb,WAAA,CAAAc,OAAA,WAAAC,IAAA;QACA,KAAAF,UAAA,CAAAE,IAAA,CAAA3D,MAAA;UACAyD,UAAA,CAAAE,IAAA,CAAA3D,MAAA;QACA;QACAyD,UAAA,CAAAE,IAAA,CAAA3D,MAAA,EAAA4D,IAAA,CAAAD,IAAA;MACA;;MAEA;MACAE,MAAA,CAAAC,MAAA,CAAAL,UAAA,EAAAC,OAAA,WAAAK,SAAA;QACA;QACAA,SAAA,CAAAC,IAAA,WAAAC,CAAA,EAAAC,CAAA;UAAA,WAAAC,IAAA,CAAAF,CAAA,CAAAG,UAAA,QAAAD,IAAA,CAAAD,CAAA,CAAAE,UAAA;QAAA;;QAEA;QACA,IAAAC,UAAA,GAAAN,SAAA;QACA,IAAAO,IAAA,GAAAf,KAAA,CAAAgB,cAAA,CAAAF,UAAA,CAAAD,UAAA;QAEA,KAAAZ,MAAA,CAAAc,IAAA;UACAd,MAAA,CAAAc,IAAA;QACA;;QAEA;QACAd,MAAA,CAAAc,IAAA,EAAAV,IAAA,KAAAY,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACAJ,UAAA;UACAK,QAAA;UACApD,UAAA,EAAAiC,KAAA,CAAArD,oBAAA,CAAAmE,UAAA,CAAArE,MAAA;UACA2E,QAAA,EAAAZ,SAAA,CAAAa,KAAA,IAAAC,GAAA,WAAAC,KAAA;YAAA,WAAAN,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACAK,KAAA;cACAJ,QAAA;YAAA;UAAA,CACA;QAAA,EACA;MACA;MAEA,OAAAlB,MAAA;IACA;EACA;EACAuB,OAAA,WAAAA,QAAA;IACAC,OAAA,CAAAC,GAAA,MAAAxF,MAAA;IACAuF,OAAA,CAAAC,GAAA,MAAAnF,MAAA;IACA,KAAAoF,aAAA,MAAAzF,MAAA;IACA,KAAA0F,eAAA;IACA,KAAAC,YAAA;EACA;EACAC,OAAA;IACAC,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,UAAAvC,OAAA;MAEA,KAAApB,WAAA;MACA;MACA,KAAAG,eAAA;MAEA,KAAAL,WAAA;MACA,KAAAG,OAAA;;MAEA,KAAA1B,WAAA,CAAAG,KAAA;MAGA,KAAAH,WAAA,CAAAE,MAAA,OAAAJ,QAAA;MACA,KAAAE,WAAA,CAAAV,MAAA,QAAAA,MAAA;MACA,KAAAU,WAAA,CAAAL,MAAA,QAAAA,MAAA;MACA,KAAAK,WAAA,CAAAC,UAAA,QAAAqB,WAAA;;MAEA;MACA,KAAAS,UAAA,QAAAnB,MAAA,CAAAyE,MAAA,WAAApC,EAAA;QAAA,OAAAA,EAAA,CAAAjC,OAAA;MAAA;;MAEA;MACA,KAAAe,UAAA,CAAAwB,OAAA,WAAAN,EAAA;QACAmC,MAAA,CAAAE,IAAA,CAAArC,EAAA;MACA;MAEA,KAAAlB,UAAA,CAAAwB,OAAA,WAAAN,EAAA;QACA,IAAAA,EAAA,CAAA7D,IAAA;UACAgG,MAAA,CAAApF,WAAA,CAAAG,KAAA,GAAAiF,MAAA,CAAApF,WAAA,CAAAG,KAAA;UACA,IAAA8C,EAAA,CAAAlC,oBAAA,CAAAwE,QAAA;YACAH,MAAA,CAAApF,WAAA,CAAAG,KAAA,GAAAiF,MAAA,CAAApF,WAAA,CAAAG,KAAA;UACA;UACA,IAAA8C,EAAA,CAAAlC,oBAAA,CAAAwE,QAAA;YACAH,MAAA,CAAApF,WAAA,CAAAG,KAAA,GAAAiF,MAAA,CAAApF,WAAA,CAAAG,KAAA;UACA;QACA;QACA,IAAA8C,EAAA,CAAA7D,IAAA;UACAgG,MAAA,CAAApF,WAAA,CAAAG,KAAA,GAAAiF,MAAA,CAAApF,WAAA,CAAAG,KAAA;UACA,IAAA8C,EAAA,CAAAlC,oBAAA,CAAAwE,QAAA;YACAH,MAAA,CAAApF,WAAA,CAAAG,KAAA,GAAAiF,MAAA,CAAApF,WAAA,CAAAG,KAAA;UACA;UACA,IAAA8C,EAAA,CAAAlC,oBAAA,CAAAwE,QAAA;YACAH,MAAA,CAAApF,WAAA,CAAAG,KAAA,GAAAiF,MAAA,CAAApF,WAAA,CAAAG,KAAA;UACA;QACA;QACA,IAAA8C,EAAA,CAAA7D,IAAA;UACAgG,MAAA,CAAApF,WAAA,CAAAG,KAAA,GAAAiF,MAAA,CAAApF,WAAA,CAAAG,KAAA;QACA;QACA,IAAA8C,EAAA,CAAA7D,IAAA;UACAgG,MAAA,CAAApF,WAAA,CAAAG,KAAA,GAAAiF,MAAA,CAAApF,WAAA,CAAAG,KAAA;QACA;QACA,IAAA8C,EAAA,CAAA7D,IAAA;UACAgG,MAAA,CAAApF,WAAA,CAAAG,KAAA,GAAAiF,MAAA,CAAApF,WAAA,CAAAG,KAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAA8C,EAAA,CAAA7D,IAAA;UACAgG,MAAA,CAAApF,WAAA,CAAAG,KAAA,GAAAiF,MAAA,CAAApF,WAAA,CAAAG,KAAA;UACA,IAAA8C,EAAA,CAAAlC,oBAAA,CAAAwE,QAAA;YACAH,MAAA,CAAApF,WAAA,CAAAG,KAAA,GAAAiF,MAAA,CAAApF,WAAA,CAAAG,KAAA;UACA;QACA;QACA,IAAA8C,EAAA,CAAA7D,IAAA;UACAgG,MAAA,CAAApF,WAAA,CAAAG,KAAA,GAAAiF,MAAA,CAAApF,WAAA,CAAAG,KAAA;UACA;UACA,IAAA8C,EAAA,CAAAlC,oBAAA,CAAAwE,QAAA;YACAH,MAAA,CAAApF,WAAA,CAAAG,KAAA,GAAAiF,MAAA,CAAApF,WAAA,CAAAG,KAAA;UACA,WAAA8C,EAAA,CAAAlC,oBAAA,CAAAwE,QAAA;YACAH,MAAA,CAAApF,WAAA,CAAAG,KAAA,GAAAiF,MAAA,CAAApF,WAAA,CAAAG,KAAA;UACA;YACAiF,MAAA,CAAApF,WAAA,CAAAG,KAAA,GAAAiF,MAAA,CAAApF,WAAA,CAAAG,KAAA;UACA;UACA;UACA,IAAA8C,EAAA,CAAAlC,oBAAA,CAAAwE,QAAA;YACAH,MAAA,CAAApF,WAAA,CAAAG,KAAA,GAAAiF,MAAA,CAAApF,WAAA,CAAAG,KAAA;UACA;QACA;MACA;MAEA0E,OAAA,CAAAC,GAAA,aAAA9E,WAAA;;MAEA;MACA,KAAAQ,aAAA,CAAAE,MAAA;MACA,KAAAF,aAAA,CAAAG,MAAA,QAAAX,WAAA;MACA,KAAAwF,OAAA,MAAAhF,aAAA;MACA,KAAAR,WAAA,CAAAO,SAAA;IACA;IAEAiF,OAAA,WAAAA,QAAAnG,IAAA;MACA,IAAAmG,aAAA,EAAAnG,IAAA,EAAAoG,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAC,GAAA,CAAAC,SAAA;YACAC,KAAA,EAAAJ,GAAA,CAAAK,QAAA;YACAC,IAAA;YACAC,QAAA;UACA;QACA;MACA;IAEA;IACAC,gBAAA,WAAAA,iBAAAjD,EAAA,EAAAkD,eAAA;MACA,KAAAlD,EAAA,CAAAjC,OAAA;MAEA,IAAAoF,KAAA,GAAAnD,EAAA,CAAAlC,oBAAA,CAAAsF,OAAA,CAAAF,eAAA;MACAtB,OAAA,CAAAC,GAAA,SAAA7B,EAAA,CAAAlC,oBAAA;;MAEA;MACA,IAAAkC,EAAA,CAAA7D,IAAA,gBAAA+G,eAAA,sBAAAA,eAAA;QACA,IAAAG,eAAA,OAAAC,mBAAA,CAAAjC,OAAA,EAAArB,EAAA,CAAAlC,oBAAA;QACA;QACA,IAAAyF,WAAA,GAAAF,eAAA,CAAAD,OAAA;QACA,IAAAI,WAAA,GAAAH,eAAA,CAAAD,OAAA;QACA,IAAAG,WAAA,SAAAF,eAAA,CAAAI,MAAA,CAAAF,WAAA;QACA,IAAAC,WAAA,SAAAH,eAAA,CAAAI,MAAA,CAAAD,WAAA;;QAEA;QACA,IAAAL,KAAA;UACAE,eAAA,CAAA7C,IAAA,CAAA0C,eAAA;QACA;QACA;;QAEA,KAAAb,IAAA,CAAArC,EAAA,0BAAAqD,eAAA;MACA;QACA;QACA,IAAAF,KAAA;UACA;UACA,KAAAd,IAAA,CAAArC,EAAA,CAAAlC,oBAAA,EAAAkC,EAAA,CAAAlC,oBAAA,CAAAgC,MAAA,EAAAoD,eAAA;QACA;UACA;UACA,IAAAG,gBAAA,OAAAC,mBAAA,CAAAjC,OAAA,EAAArB,EAAA,CAAAlC,oBAAA;UACAuF,gBAAA,CAAAI,MAAA,CAAAN,KAAA;UACA,KAAAd,IAAA,CAAArC,EAAA,0BAAAqD,gBAAA;QACA;MACA;MAEAzB,OAAA,CAAAC,GAAA,SAAA7B,EAAA,CAAAlC,oBAAA;MACA,KAAA4F,YAAA;IACA;IACAC,aAAA,WAAAA,cAAA3F,MAAA;MACA,QAAAA,MAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;MACA;IACA;IACA4F,aAAA,WAAAA,cAAA5F,MAAA;MACA,QAAAA,MAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;MACA;IACA;IACA6F,cAAA,WAAAA,eAAAC,IAAA;MACA,WAAAC,cAAA,EAAAD,IAAA;IACA;IACA;IACAE,UAAA,WAAAA,WAAAC,IAAA;MACA,IAAAC,OAAA,GAAAC,QAAA,CAAAC,aAAA;MACAF,OAAA,CAAAG,SAAA,GAAAJ,IAAA;MACA,OAAAC,OAAA,CAAAI,WAAA,IAAAJ,OAAA,CAAAK,SAAA;IACA;IAEA;IACAC,cAAA,WAAAA,eAAAP,IAAA;MACA,YAAAlF,eAAA,CAAA0F,QAAA,CAAAR,IAAA;IACA;IAEAS,UAAA,WAAAA,WAAAC,OAAA;MACA;MACA,IAAAC,SAAA,QAAAZ,UAAA,CAAAW,OAAA;MACA,IAAAE,QAAA,GAAAV,QAAA,CAAAC,aAAA;MACAS,QAAA,CAAAzG,KAAA,GAAAwG,SAAA;MACAT,QAAA,CAAAW,IAAA,CAAAC,WAAA,CAAAF,QAAA;MACAA,QAAA,CAAAG,MAAA;MACAb,QAAA,CAAAc,WAAA;MACAd,QAAA,CAAAW,IAAA,CAAAI,WAAA,CAAAL,QAAA;MACA,KAAAM,QAAA,CAAAC,OAAA;IACA;IAEAC,YAAA,WAAAA,aAAAC,MAAA;MACA;MACA,IAAAC,QAAA,GAAAD,MAAA,CAAAX,OAAA;MACA,IAAAa,IAAA,OAAAC,IAAA,EAAAF,QAAA;QAAAG,IAAA;MAAA;MACA,IAAAC,IAAA,GAAAxB,QAAA,CAAAC,aAAA;MACAuB,IAAA,CAAAC,IAAA,GAAAC,GAAA,CAAAC,eAAA,CAAAN,IAAA;MACAG,IAAA,CAAAI,QAAA,MAAAC,MAAA,CAAAV,MAAA,CAAAW,MAAA,oBAAAD,MAAA,KAAAjF,IAAA,GAAAmF,WAAA,GAAA1E,KAAA;MACAmE,IAAA,CAAAQ,KAAA;MACAN,GAAA,CAAAO,eAAA,CAAAT,IAAA,CAAAC,IAAA;MACA,KAAAT,QAAA,CAAAC,OAAA;IACA;IAEAiB,YAAA,WAAAA,aAAAC,QAAA;MACA,IAAAA,QAAA;QACAC,MAAA,CAAAC,IAAA,CAAAF,QAAA;MACA;QACA,KAAAnB,QAAA,CAAAsB,OAAA;MACA;IACA;IACAC,cAAA,WAAAA,eAAAC,QAAA;MAAA,IAAAC,MAAA;MACA,KAAA/H,iBAAA,GAAA8H,QAAA;MACA,KAAA/H,eAAA;MACA;MACA,IAAAiI,YAAA,QAAArI,WAAA,CAAA4E,OAAA,CAAAuD,QAAA;MACA,IAAAE,YAAA;QACA,KAAAC,SAAA;UACA,IAAAC,QAAA,GAAAH,MAAA,CAAAI,GAAA,CAAAC,aAAA;UACA,IAAAF,QAAA,IAAAA,QAAA,CAAAG,OAAA;YACAH,QAAA,CAAAG,OAAA,CAAAC,aAAA,CAAAN,YAAA;UACA;QACA;MACA;IACA;IACAO,eAAA,WAAAA,gBAAA;MACA,KAAAxI,eAAA;MACA,KAAAC,iBAAA;IACA;IACA;IACAiD,aAAA,WAAAA,cAAArF,EAAA;MAAA,IAAA4K,MAAA;MACA,IAAAC,KAAA,GAAAC,OAAA,CAAAC,GAAA,CAAAC,cAAA,WAAAzB,MAAA,CAAAvJ,EAAA;MACAmF,OAAA,CAAAC,GAAA,mBAAA0F,OAAA,CAAAC,GAAA,CAAAC,cAAA;MACAC,kBAAA,CAAAC,OAAA,CAAAL,KAAA,YAAAM,KAAA;QACA,QAAAA,KAAA,CAAAlC,IAAA;UACA;YACA;YACA;UACA;YACA2B,MAAA,CAAAQ,sBAAA,CAAAD,KAAA,CAAAxL,IAAA;YACA;UACA;YACAiL,MAAA,CAAAlC,QAAA,CAAAsB,OAAA;YACA;UACA;YACAY,MAAA,CAAAlC,QAAA,CAAA2C,KAAA;YACA;UACA;YACAT,MAAA,CAAAlC,QAAA,CAAA2C,KAAA;YACA;QACA;MACA;IACA;IAEAD,sBAAA,WAAAA,uBAAAzL,IAAA;MAEA,IAAA2L,OAAA,GAAA3L,IAAA;MACA,IAAA4L,OAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAH,OAAA;;MAEA;MACA,IAAAC,OAAA,CAAAtC,IAAA,6BAAAsC,OAAA,CAAApL,MAAA;QACA,KAAAG,WAAA,CAAAI,UAAA,GAAA6K,OAAA,CAAApL,MAAA;MACA,WAAAoL,OAAA,CAAAtC,IAAA,6BAAAsC,OAAA,CAAApL,MAAA;QACA,KAAAG,WAAA,CAAAK,UAAA,GAAA4K,OAAA,CAAApL,MAAA;MACA,WAAAoL,OAAA,CAAAtC,IAAA,2BAAAsC,OAAA,CAAApL,MAAA;QACA,KAAAG,WAAA,CAAAM,QAAA,GAAA2K,OAAA,CAAApL,MAAA;MACA;;MAEA;MACA,IAAAoL,OAAA,CAAAtC,IAAA,6BAAAsC,OAAA,CAAA/B,MAAA;QACA,IAAAkC,SAAA,QAAArJ,UAAA,CAAAsJ,IAAA,WAAApI,EAAA;UAAA,OAAAA,EAAA,CAAA7D,IAAA,KAAA6L,OAAA,CAAA/B,MAAA;QAAA;QACA,IAAAkC,SAAA;UACA;UACAA,SAAA,CAAAlK,YAAA,CAAAoK,OAAA;YACA1D,OAAA,EAAAqD,OAAA,CAAArD,OAAA;YACA2D,SAAA,MAAAvH,IAAA;YACAwH,WAAA;UACA;QACA;QACA;MACA;;MAEA;MACA,IAAAP,OAAA,CAAAtC,IAAA,6BAAAsC,OAAA,CAAAQ,GAAA;QACA;QACA,KAAAhK,WAAA,CAAA6J,OAAA,CAAAL,OAAA,CAAAQ,GAAA;QACA;MACA;;MAEA;MACA,IAAAR,OAAA,CAAAtC,IAAA;QACA,IAAA+C,MAAA,QAAA3J,UAAA,CAAAsJ,IAAA,WAAApI,EAAA;UAAA,OAAAA,EAAA,CAAA7D,IAAA;QAAA;QACA,IAAAsM,MAAA;UACA,KAAApG,IAAA,CAAAoG,MAAA;UACA,IAAAA,MAAA,CAAAxK,YAAA,CAAA6B,MAAA;YACA,KAAAuC,IAAA,CAAAoG,MAAA,CAAAxK,YAAA;UACA;UACA;UACA,KAAAQ,OAAA,CAAA4J,OAAA;YACApC,MAAA;YACAtB,OAAA,EAAAqD,OAAA,CAAAU,YAAA;YACApC,QAAA,EAAA0B,OAAA,CAAA1B,QAAA;YACAqC,WAAA,EAAAX,OAAA,CAAAW,WAAA;YACAL,SAAA,MAAAvH,IAAA;UACA;UACA,KAAArC,eAAA;;UAEA;UACA,KAAAkK,WAAA;QACA;QACA;MACA;;MAEA;MACA,IAAAT,QAAA;MACA,QAAAH,OAAA,CAAAtC,IAAA;QACA;UACA9D,OAAA,CAAAC,GAAA,UAAAzF,IAAA;UACA+L,QAAA,QAAArJ,UAAA,CAAAsJ,IAAA,WAAApI,EAAA;YAAA,OAAAA,EAAA,CAAA7D,IAAA;UAAA;UACA;QACA;UACAyF,OAAA,CAAAC,GAAA,UAAAzF,IAAA;UACA+L,QAAA,QAAArJ,UAAA,CAAAsJ,IAAA,WAAApI,EAAA;YAAA,OAAAA,EAAA,CAAA7D,IAAA;UAAA;UACA;QACA;UACAyF,OAAA,CAAAC,GAAA,UAAAzF,IAAA;UACA+L,QAAA,QAAArJ,UAAA,CAAAsJ,IAAA,WAAApI,EAAA;YAAA,OAAAA,EAAA,CAAA7D,IAAA;UAAA;UACA;QACA;UACAyF,OAAA,CAAAC,GAAA,UAAAzF,IAAA;UACA+L,QAAA,QAAArJ,UAAA,CAAAsJ,IAAA,WAAApI,EAAA;YAAA,OAAAA,EAAA,CAAA7D,IAAA;UAAA;UACA;QACA;UACAyF,OAAA,CAAAC,GAAA,UAAAzF,IAAA;UACA+L,QAAA,QAAArJ,UAAA,CAAAsJ,IAAA,WAAApI,EAAA;YAAA,OAAAA,EAAA,CAAA7D,IAAA;UAAA;UACA;QACA;UACAyF,OAAA,CAAAC,GAAA,UAAAzF,IAAA;UACA+L,QAAA,QAAArJ,UAAA,CAAAsJ,IAAA,WAAApI,EAAA;YAAA,OAAAA,EAAA,CAAA7D,IAAA;UAAA;UACA;QACA;QACA;QACA;MACA;MAEA,IAAAgM,QAAA;QACA;QACA,KAAA9F,IAAA,CAAA8F,QAAA;;QAEA;QACA,IAAAA,QAAA,CAAAlK,YAAA,CAAA6B,MAAA;UACA,KAAAuC,IAAA,CAAA8F,QAAA,CAAAlK,YAAA;QACA;;QAEA;QACA,IAAA4K,WAAA,QAAApK,OAAA,CAAAqK,SAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAA9C,MAAA,KAAAkC,QAAA,CAAAhM,IAAA;QAAA;QACA,IAAA0M,WAAA;UACA,KAAApK,OAAA,CAAA4J,OAAA;YACApC,MAAA,EAAAkC,QAAA,CAAAhM,IAAA;YACAwI,OAAA,EAAAqD,OAAA,CAAAU,YAAA;YACApC,QAAA,EAAA0B,OAAA,CAAA1B,QAAA;YACAqC,WAAA,EAAAX,OAAA,CAAAW,WAAA;YACAL,SAAA,MAAAvH,IAAA;UACA;UACA,KAAArC,eAAA;QACA;UACA,KAAAD,OAAA,CAAAgF,MAAA,CAAAoF,WAAA;UACA,KAAApK,OAAA,CAAA4J,OAAA;YACApC,MAAA,EAAAkC,QAAA,CAAAhM,IAAA;YACAwI,OAAA,EAAAqD,OAAA,CAAAU,YAAA;YACApC,QAAA,EAAA0B,OAAA,CAAA1B,QAAA;YACAqC,WAAA,EAAAX,OAAA,CAAAW,WAAA;YACAL,SAAA,MAAAvH,IAAA;UACA;UACA,KAAArC,eAAA;QACA;QACA,KAAAkK,WAAA;MACA;;MAEA;MACA;MACA;MACA;;MAEA;MACA;MACA;IACA;IAEAI,cAAA,WAAAA,eAAA;MACAtB,kBAAA,CAAAuB,KAAA;IACA;IAEAC,WAAA,WAAAA,YAAA9M,IAAA;MAAA,IAAA+M,MAAA;MACA,IAAAzB,kBAAA,CAAA0B,IAAA,CAAAhN,IAAA;QACA;QACA,KAAA0K,SAAA;UACAqC,MAAA,CAAAE,cAAA;QACA;MACA;QACA,KAAAlE,QAAA,CAAA2C,KAAA;MACA;IACA;IACAwB,iBAAA,WAAAA,kBAAAtJ,EAAA;MACA,KAAAqC,IAAA,CAAArC,EAAA,iBAAAA,EAAA,CAAA9B,UAAA;IACA;IAEAqL,UAAA,WAAAA,WAAAjB,SAAA;MACA,IAAApH,IAAA,OAAAH,IAAA,CAAAuH,SAAA;MACA,OAAApH,IAAA,CAAAsI,kBAAA;QACAC,IAAA;QACAC,MAAA;QACAC,MAAA;QACAC,MAAA;MACA;IACA;IACAC,eAAA,WAAAA,gBAAA;MACA,KAAAzK,kBAAA;MACA,KAAAC,eAAA;IACA;IAEAyK,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,UAAA9J,QAAA;;MAEA;MACA,IAAA+J,gBAAA,QAAAvL,OAAA,CACA2D,MAAA,WAAAkD,MAAA;QAAA,OAAAyE,MAAA,CAAA1K,eAAA,CAAAiD,QAAA,CAAAgD,MAAA,CAAAW,MAAA;MAAA,GACAxE,GAAA,WAAA6D,MAAA;QACA;QACA,IAAA2E,YAAA,GAAAF,MAAA,CAAA/F,UAAA,CAAAsB,MAAA,CAAAX,OAAA;QACA,UAAAqB,MAAA,CAAAV,MAAA,CAAAW,MAAA,0BAAAD,MAAA,CAAAiE,YAAA;MACA,GACAC,IAAA;;MAEA;MACA,IAAAC,UAAA,MAAAnE,MAAA,MAAA1G,WAAA,QAAA0G,MAAA,CAAAgE,gBAAA;;MAEA;MACA,IAAAI,YAAA;QACA5M,OAAA;QACAf,EAAA,MAAAI,QAAA;QACAY,MAAA;QACAC,MAAA;UACAT,MAAA,MAAAJ,QAAA;UACAR,MAAA,OAAAA,MAAA;UACAK,MAAA,OAAAA,MAAA;UACAM,UAAA,EAAAmN,UAAA;UACAjN,KAAA;QACA;MACA;;MAEA;MACA0E,OAAA,CAAAC,GAAA,OAAAuI,YAAA;MACA,KAAA7H,OAAA,CAAA6H,YAAA;MACA,KAAAhL,kBAAA;;MAEA;MACA,IAAAqJ,MAAA;QACAtM,IAAA;QACAyB,MAAA,EAAAhC,OAAA;QACAiC,YAAA,GACA;UAAAM,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,EACA;QACAN,oBAAA;QACAC,OAAA;QACAC,MAAA;QACAC,YAAA,GACA;UACA0G,OAAA;UACA2D,SAAA,MAAAvH,IAAA;UACAwH,WAAA;UACA7C,IAAA;QACA,EACA;QACAxH,UAAA;MACA;;MAEA;MACA,IAAAmM,UAAA,QAAAvL,UAAA,CAAAgK,SAAA,WAAA9I,EAAA;QAAA,OAAAA,EAAA,CAAA7D,IAAA;MAAA;MACA,IAAAkO,UAAA;QACA;QACA,KAAAvL,UAAA,CAAAuJ,OAAA,CAAAI,MAAA;MACA;QACA;QACA,KAAA3J,UAAA,CAAAuL,UAAA,IAAA5B,MAAA;QACA;QACA,IAAA6B,IAAA,QAAAxL,UAAA,CAAA2E,MAAA,CAAA4G,UAAA;QACA,KAAAvL,UAAA,CAAAuJ,OAAA,CAAAiC,IAAA;MACA;MAEA,KAAA5G,YAAA;MACA,KAAAyB,QAAA,CAAAC,OAAA;IACA;IACA;IACAmF,iBAAA,WAAAA,kBAAA;MACA,KAAAhL,oBAAA;MACA,KAAAwC,eAAA;IACA;IAEA;IACAyI,wBAAA,WAAAA,yBAAA;MACA,KAAAjL,oBAAA;IACA;IAEA;IACAwC,eAAA,WAAAA,gBAAA0I,KAAA;MAAA,IAAAC,MAAA;MAAA,WAAAC,kBAAA,CAAAtJ,OAAA,mBAAAuJ,aAAA,CAAAvJ,OAAA,IAAAwJ,CAAA,UAAAC,QAAA;QAAA,IAAArI,GAAA,EAAAsI,EAAA;QAAA,WAAAH,aAAA,CAAAvJ,OAAA,IAAA2J,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA;YAAA;cAAAD,QAAA,CAAAE,CAAA;cAAAF,QAAA,CAAAC,CAAA;cAAA,OAEA,IAAAE,oBAAA,EAAAV,MAAA,CAAArO,MAAA,EAAAoO,KAAA;YAAA;cAAAhI,GAAA,GAAAwI,QAAA,CAAAI,CAAA;cACA,IAAA5I,GAAA,CAAAC,IAAA;gBACAgI,MAAA,CAAAlL,WAAA,GAAAiD,GAAA,CAAArG,IAAA;cACA;cAAA6O,QAAA,CAAAC,CAAA;cAAA;YAAA;cAAAD,QAAA,CAAAE,CAAA;cAAAJ,EAAA,GAAAE,QAAA,CAAAI,CAAA;cAEAzJ,OAAA,CAAAkG,KAAA,cAAAiD,EAAA;cACAL,MAAA,CAAAvF,QAAA,CAAA2C,KAAA;YAAA;cAAA,OAAAmD,QAAA,CAAApK,CAAA;UAAA;QAAA,GAAAiK,OAAA;MAAA;IAEA;IAEA;IACAQ,iBAAA,WAAAA,kBAAAhD,SAAA;MACA,IAAApH,IAAA,OAAAH,IAAA,CAAAuH,SAAA;MACA,OAAApH,IAAA,CAAAsI,kBAAA;QACAC,IAAA;QACAC,MAAA;QACAE,MAAA;MACA;IACA;IAEA;IACAzI,cAAA,WAAAA,eAAAmH,SAAA;MACA,IAAApH,IAAA,OAAAH,IAAA,CAAAuH,SAAA;MACA,IAAAiD,KAAA,OAAAxK,IAAA;MACA,IAAAyK,SAAA,OAAAzK,IAAA,CAAAwK,KAAA;MACAC,SAAA,CAAAC,OAAA,CAAAD,SAAA,CAAAE,OAAA;MACA,IAAAC,UAAA,OAAA5K,IAAA,CAAAwK,KAAA;MACAI,UAAA,CAAAF,OAAA,CAAAE,UAAA,CAAAD,OAAA;MACA,IAAAE,YAAA,OAAA7K,IAAA,CAAAwK,KAAA;MACAK,YAAA,CAAAH,OAAA,CAAAG,YAAA,CAAAF,OAAA;MAEA,IAAAxK,IAAA,CAAA2K,YAAA,OAAAN,KAAA,CAAAM,YAAA;QACA;MACA,WAAA3K,IAAA,CAAA2K,YAAA,OAAAL,SAAA,CAAAK,YAAA;QACA;MACA,WAAA3K,IAAA,CAAA2K,YAAA,OAAAF,UAAA,CAAAE,YAAA;QACA;MACA,WAAA3K,IAAA,CAAA2K,YAAA,OAAAD,YAAA,CAAAC,YAAA;QACA;MACA;QACA,OAAA3K,IAAA,CAAA4K,kBAAA;UACAC,IAAA;UACAC,KAAA;UACAC,GAAA;QACA;MACA;IACA;IAEA;IACAC,eAAA,WAAAA,gBAAA3L,IAAA;MACA;QACA,IAAA4L,WAAA,GAAAlE,IAAA,CAAAC,KAAA,CAAA3H,IAAA,CAAAnE,IAAA;QACA;QACA,KAAAuB,MAAA,GAAAwO,WAAA,CAAAxO,MAAA,SAAAA,MAAA;QACA;QACA,KAAAU,WAAA,GAAA8N,WAAA,CAAA9N,WAAA;QACA;QACA,KAAAS,UAAA,GAAAqN,WAAA,CAAArN,UAAA;QACA;QACA,KAAAN,WAAA,GAAA2N,WAAA,CAAA3N,WAAA;QACA;QACA,KAAAC,OAAA,GAAA0N,WAAA,CAAA1N,OAAA;QACA;QACA,KAAA7B,MAAA,GAAA2D,IAAA,CAAA3D,MAAA,SAAAA,MAAA;QACA,KAAAG,WAAA,CAAAI,UAAA,GAAAoD,IAAA,CAAApD,UAAA;QACA,KAAAJ,WAAA,CAAAK,UAAA,GAAAmD,IAAA,CAAAnD,UAAA;QACA,KAAAL,WAAA,CAAAM,QAAA,GAAAkD,IAAA,CAAAlD,QAAA;QACA,KAAAN,WAAA,CAAAO,SAAA;;QAEA;QACA,KAAAqB,eAAA;QACA,KAAAL,WAAA;QAEA,KAAA6G,QAAA,CAAAC,OAAA;QACA,KAAA7F,oBAAA;MACA,SAAAuI,KAAA;QACAlG,OAAA,CAAAkG,KAAA,cAAAA,KAAA;QACA,KAAA3C,QAAA,CAAA2C,KAAA;MACA;IACA;IAEA;IACAc,WAAA,WAAAA,YAAA;MAAA,IAAAwD,MAAA;MAAA,WAAAzB,kBAAA,CAAAtJ,OAAA,mBAAAuJ,aAAA,CAAAvJ,OAAA,IAAAwJ,CAAA,UAAAwB,SAAA;QAAA,IAAAF,WAAA,EAAAG,GAAA;QAAA,WAAA1B,aAAA,CAAAvJ,OAAA,IAAA2J,CAAA,WAAAuB,SAAA;UAAA,kBAAAA,SAAA,CAAArB,CAAA;YAAA;cACA;cACA;cACA;cAEAiB,WAAA;gBACAxO,MAAA,EAAAyO,MAAA,CAAAzO,MAAA;gBACAU,WAAA,EAAA+N,MAAA,CAAA/N,WAAA;gBACAS,UAAA,EAAAsN,MAAA,CAAAtN,UAAA;gBACAN,WAAA,EAAA4N,MAAA,CAAA5N,WAAA;gBACAC,OAAA,EAAA2N,MAAA,CAAA3N,OAAA;gBACA7B,MAAA,EAAAwP,MAAA,CAAAxP,MAAA;gBACAO,UAAA,EAAAiP,MAAA,CAAArP,WAAA,CAAAI,UAAA;gBACAC,UAAA,EAAAgP,MAAA,CAAArP,WAAA,CAAAK,UAAA;gBACAC,QAAA,EAAA+O,MAAA,CAAArP,WAAA,CAAAM;cACA;cAAAkP,SAAA,CAAApB,CAAA;cAAAoB,SAAA,CAAArB,CAAA;cAAA,OAGA,IAAAsB,sBAAA;gBACAnQ,MAAA,EAAA+P,MAAA,CAAA/P,MAAA;gBACAW,UAAA,EAAAoP,MAAA,CAAA/N,WAAA;gBACAjC,IAAA,EAAA6L,IAAA,CAAAwE,SAAA,CAAAN,WAAA;gBACAvP,MAAA,EAAAwP,MAAA,CAAAxP,MAAA;gBACAO,UAAA,EAAAiP,MAAA,CAAArP,WAAA,CAAAI,UAAA;gBACAC,UAAA,EAAAgP,MAAA,CAAArP,WAAA,CAAAK,UAAA;gBACAC,QAAA,EAAA+O,MAAA,CAAArP,WAAA,CAAAM;cACA;YAAA;cAAAkP,SAAA,CAAArB,CAAA;cAAA;YAAA;cAAAqB,SAAA,CAAApB,CAAA;cAAAmB,GAAA,GAAAC,SAAA,CAAAlB,CAAA;cAEAzJ,OAAA,CAAAkG,KAAA,cAAAwE,GAAA;cACAF,MAAA,CAAAjH,QAAA,CAAA2C,KAAA;YAAA;cAAA,OAAAyE,SAAA,CAAA1L,CAAA;UAAA;QAAA,GAAAwL,QAAA;MAAA;IAEA;IAEA;IACAK,sBAAA,WAAAA,uBAAAnM,IAAA;MACA,KAAA8B,IAAA,MAAAvF,oBAAA,EAAAyD,IAAA,CAAA3D,MAAA,QAAAE,oBAAA,CAAAyD,IAAA,CAAA3D,MAAA;IACA;IAEA;IACA+P,aAAA,WAAAA,cAAA;MACA;MACA,KAAA/P,MAAA,OAAAC,QAAA;MACA,KAAAS,SAAA;MACA,KAAAe,WAAA;MACA,KAAAC,WAAA;MACA,KAAAE,WAAA;MACA,KAAAC,OAAA;MACA,KAAAK,UAAA;MACA,KAAA/B,WAAA;QACAC,UAAA;QACAX,MAAA,OAAAA,MAAA;QACAK,MAAA,OAAAA,MAAA;QACAO,MAAA;QACAC,KAAA;QACAC,UAAA;QACAC,UAAA;QACAC,QAAA;QACAC,SAAA;MACA;MACA;MACA,KAAAK,MAAA,IACA;QACAxB,IAAA;QACAyB,MAAA,EAAAhC,OAAA;QACAiC,YAAA;QACAC,oBAAA;QACAC,OAAA;QACAC,MAAA;QACAC,YAAA;QACAC,UAAA;MACA,GACA;QACA/B,IAAA;QACAyB,MAAA,EAAAhC,OAAA;QACAiC,YAAA;QACAC,oBAAA;QACAC,OAAA;QACAC,MAAA;QACAC,YAAA;QACAC,UAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA/B,IAAA;QACAyB,MAAA,EAAAhC,OAAA;QACAiC,YAAA,GACA;UAAAM,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,EACA;QACAN,oBAAA;QACAC,OAAA;QACAC,MAAA;QACAC,YAAA;QACAC,UAAA;MACA,GACA;QACA/B,IAAA;QACAyB,MAAA,EAAAhC,OAAA;QACAiC,YAAA,GACA;UAAAM,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,EACA;QACAN,oBAAA;QACAC,OAAA;QACAC,MAAA;QACAC,YAAA;QACAC,UAAA;MACA,GACA;QACA/B,IAAA;QACAyB,MAAA,EAAAhC,OAAA;QACAiC,YAAA,GACA;UAAAM,KAAA;UAAAC,KAAA;QAAA,EACA;QACAN,oBAAA;QACAC,OAAA;QACAC,MAAA;QACAC,YAAA;QACAC,UAAA;MACA,GACA;QACA/B,IAAA;QACAyB,MAAA,EAAAhC,OAAA;QACAiC,YAAA,GACA;UAAAM,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,EACA;QACAN,oBAAA;QACAC,OAAA;QACAC,MAAA;QACAC,YAAA;QACAC,UAAA;MACA,EACA;MACA;MACA,KAAAS,eAAA;MAEA,KAAAwG,QAAA,CAAAC,OAAA;IACA;IAEA;IACApD,YAAA,WAAAA,aAAA;MAAA,IAAA4K,MAAA;MAAA,WAAAjC,kBAAA,CAAAtJ,OAAA,mBAAAuJ,aAAA,CAAAvJ,OAAA,IAAAwJ,CAAA,UAAAgC,SAAA;QAAA,IAAApK,GAAA,EAAAqK,QAAA,EAAAC,GAAA;QAAA,WAAAnC,aAAA,CAAAvJ,OAAA,IAAA2J,CAAA,WAAAgC,SAAA;UAAA,kBAAAA,SAAA,CAAA9B,CAAA;YAAA;cAAA8B,SAAA,CAAA7B,CAAA;cAAA6B,SAAA,CAAA9B,CAAA;cAAA,OAEA,IAAAE,oBAAA,EAAAwB,MAAA,CAAAvQ,MAAA;YAAA;cAAAoG,GAAA,GAAAuK,SAAA,CAAA3B,CAAA;cACA,IAAA5I,GAAA,CAAAC,IAAA,YAAAD,GAAA,CAAArG,IAAA,IAAAqG,GAAA,CAAArG,IAAA,CAAA0D,MAAA;gBACA;gBACAgN,QAAA,GAAArK,GAAA,CAAArG,IAAA;gBACAwQ,MAAA,CAAAV,eAAA,CAAAY,QAAA;cACA;cAAAE,SAAA,CAAA9B,CAAA;cAAA;YAAA;cAAA8B,SAAA,CAAA7B,CAAA;cAAA4B,GAAA,GAAAC,SAAA,CAAA3B,CAAA;cAEAzJ,OAAA,CAAAkG,KAAA,cAAAiF,GAAA;YAAA;cAAA,OAAAC,SAAA,CAAAnM,CAAA;UAAA;QAAA,GAAAgM,QAAA;MAAA;IAEA;IAEA;IACAI,WAAA,WAAAA,YAAAzE,GAAA;MACA,KAAAA,GAAA;MACA,IAAA0E,eAAA;MACA,IAAAC,QAAA,GAAA3E,GAAA,CAAA4E,WAAA;MACA,OAAAF,eAAA,CAAAnN,IAAA,WAAAsN,GAAA;QAAA,OAAAF,QAAA,CAAA7K,QAAA,CAAA+K,GAAA;MAAA;IACA;IAEA;IACAC,SAAA,WAAAA,UAAA9E,GAAA;MACA,KAAAA,GAAA;MACA,OAAAA,GAAA,CAAA4E,WAAA,GAAA9K,QAAA;IACA;IAEA;IACAiL,aAAA,WAAAA,cAAAtH,MAAA;MACA,IAAAuH,QAAA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MAEA,IAAAC,KAAA,GAAAD,QAAA,CAAAvH,MAAA;;MAEA;QACAwH,KAAA,EAAAA,KAAA;QACAC,MAAA;MACA;IACA;IAEA;IACAC,kBAAA,WAAAA,mBAAArI,MAAA;MAAA,IAAAsI,MAAA;MACA,SAAAlO,eAAA;;MAEA,KAAAA,eAAA;MACA,KAAAD,aAAA;;MAEA,IAAA/B,MAAA;QACAmQ,WAAA,EAAAvI,MAAA,CAAAX,OAAA;QACA2B,QAAA,EAAAhB,MAAA,CAAAgB,QAAA;QACAjK,MAAA,OAAAA,MAAA;QACAyR,GAAA,OAAArO,aAAA;QACAwG,MAAA,EAAAX,MAAA,CAAAW;MACA;MAEA,IAAA8H,oBAAA,EAAArQ,MAAA,EAAA8E,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAkL,MAAA,CAAAzI,QAAA,CAAAC,OAAA;QACA;UACAwI,MAAA,CAAAzI,QAAA,CAAA2C,KAAA,CAAArF,GAAA,CAAAuL,GAAA;QACA;MACA,GAAAC,KAAA,WAAAnG,KAAA;QACAlG,OAAA,CAAAkG,KAAA,cAAAA,KAAA;QACA8F,MAAA,CAAAzI,QAAA,CAAA2C,KAAA;MACA,GAAAoG,OAAA;QACAN,MAAA,CAAAlO,eAAA;MACA;IACA;EAGA;AACA", "ignoreList": []}]}