{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:////Users/<USER>/AGI/u3w/U3W-AI/cube-mini/App.vue?736d", "uni-app:///App.vue", "webpack:////Users/<USER>/AGI/u3w/U3W-AI/cube-mini/App.vue?8efd", "webpack:////Users/<USER>/AGI/u3w/U3W-AI/cube-mini/App.vue?8418"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "<PERSON><PERSON>", "use", "plugins", "config", "productionTip", "prototype", "$store", "store", "App", "mpType", "app", "$mount", "onLaunch", "methods", "initData", "uni", "success", "statusBarHeight", "menuWidth", "menuHeight", "menuBorderRadius", "menuRight", "menuTop", "contentTop", "fail", "console", "initApp", "initConfig", "checkLogin"], "mappings": ";;;;;;;;;;;;;;AAAA;AAE2D;AAAgD;AAC3G;AACA;AACA;AACA;AAAqB;AAAA;AALrB;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAIpC;AACtBC,YAAG,CAACC,GAAG,CAACC,gBAAO,CAAC;AAEhBF,YAAG,CAACG,MAAM,CAACC,aAAa,GAAG,KAAK;AAChCJ,YAAG,CAACK,SAAS,CAACC,MAAM,GAAGC,cAAK;AAE5BC,YAAG,CAACC,MAAM,GAAG,KAAK;AAElB,IAAMC,GAAG,GAAG,IAAIV,YAAG,mBACdQ,YAAG,EACN;AAEF,UAAAE,GAAG,EAACC,MAAM,EAAE,C;;;;;;;;;;;;;AClBZ;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACuD;AACL;AACc;;;AAGhE;AAC6M;AAC7M,gBAAgB,iNAAU;AAC1B,EAAE,yEAAM;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAkzB,CAAgB,iyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACCt0B;AACA;AACA;AAEA,eAEA;EACAC;IACA;IACA;EACA;EACAC;IACAC;MACAC;QACAC;UACA;UACA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UAEA;YACAC;YAAA;YACAC;YAAA;YACAC;YAAA;YACAC;YAAA;YACAC;YAAA;YACAC;YAAA;YACAC;UACA;;UACAR;QACA;QACAS;UACAC;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;IAIA;IACAC;MACA;IACA;IACAC;MACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvEA;AAAA;AAAA;AAAA;AAAqgD,CAAgB,g6CAAG,EAAC,C;;;;;;;;;;;ACAzhD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "common/main.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';import Vue from 'vue'\nimport App from './App'\nimport store from './store' // store\nimport plugins from './plugins' // plugins\nimport './permission' // permission\nVue.use(plugins)\n\nVue.config.productionTip = false\nVue.prototype.$store = store\n\nApp.mpType = 'app'\n\nconst app = new Vue({\n  ...App\n})\n\napp.$mount()", "var render, staticRenderFns, recyclableRender, components\nvar renderjs\nimport script from \"./App.vue?vue&type=script&lang=js&\"\nexport * from \"./App.vue?vue&type=script&lang=js&\"\nimport style0 from \"./App.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"App.vue\"\nexport default component.exports", "import mod from \"-!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=script&lang=js&\"", "<script>\n\timport config from './config'\n\timport store from '@/store'\n\timport {\n\t\tgetToken\n\t} from '@/utils/auth'\n\n\texport default {\n\t\tonLaunch: function() {\n\t\t\tthis.initApp();\n\t\t\tthis.initData();\n\t\t},\n\t\tmethods: {\n\t\t\tinitData() {\n\t\t\t\tuni.getSystemInfo({\n\t\t\t\t\tsuccess: (result) => {\n\t\t\t\t\t\t// 获取手机系统的状态栏高度（不同手机的状态栏高度不同）  （ 不要使用uni-app官方文档的var(--status-bar-height) 官方这个是固定的20px  不对的 ）\n\t\t\t\t\t\t// console.log('当前手机的状态栏高度',result.statusBarHeight)\n\t\t\t\t\t\tlet statusBarHeight = result.statusBarHeight + 'px'\n\n\t\t\t\t\t\t// 获取右侧胶囊的信息 单位px\n\t\t\t\t\t\tconst menuButtonInfo = uni.getMenuButtonBoundingClientRect()\n\t\t\t\t\t\t//bottom: 胶囊底部距离屏幕顶部的距离\n\t\t\t\t\t\t//height: 胶囊高度\n\t\t\t\t\t\t//left:   胶囊左侧距离屏幕左侧的距离\n\t\t\t\t\t\t//right:  胶囊右侧距离屏幕左侧的距离\n\t\t\t\t\t\t//top:    胶囊顶部距离屏幕顶部的距离\n\t\t\t\t\t\t//width:  胶囊宽度\n\t\t\t\t\t\t// console.log(menuButtonInfo.width, menuButtonInfo.height, menuButtonInfo.top)\n\t\t\t\t\t\t// console.log('计算胶囊右侧距离屏幕右边距离', result.screenWidth - menuButtonInfo.right)\n\t\t\t\t\t\tlet menuWidth = menuButtonInfo.width + 'px'\n\t\t\t\t\t\tlet menuHeight = menuButtonInfo.height + 'px'\n\t\t\t\t\t\tlet menuBorderRadius = menuButtonInfo.height / 2 + 'px'\n\t\t\t\t\t\tlet menuRight = result.screenWidth - menuButtonInfo.right + 'px'\n\t\t\t\t\t\tlet menuTop = menuButtonInfo.top + 'px'\n\t\t\t\t\t\tlet contentTop = result.statusBarHeight + 44 + 'px'\n\n\t\t\t\t\t\tlet menuInfo = {\n\t\t\t\t\t\t\tstatusBarHeight: statusBarHeight, //状态栏高度----用来给自定义导航条页面的顶部导航条设计padding-top使用：目的留出系统的状态栏区域\n\t\t\t\t\t\t\tmenuWidth: menuWidth, //右侧的胶囊宽度--用来给自定义导航条页面的左侧胶囊设置使用\n\t\t\t\t\t\t\tmenuHeight: menuHeight, //右侧的胶囊高度--用来给自定义导航条页面的左侧胶囊设置使用\n\t\t\t\t\t\t\tmenuBorderRadius: menuBorderRadius, //一半的圆角--用来给自定义导航条页面的左侧胶囊设置使用\n\t\t\t\t\t\t\tmenuRight: menuRight, //右侧的胶囊距离右侧屏幕距离--用来给自定义导航条页面的左侧胶囊设置使用\n\t\t\t\t\t\t\tmenuTop: menuTop, //右侧的胶囊顶部距离屏幕顶部的距离--用来给自定义导航条页面的左侧胶囊设置使用\n\t\t\t\t\t\t\tcontentTop: contentTop, //内容区距离页面最上方的高度--用来给自定义导航条页面的内容区定位距离使用\n\t\t\t\t\t\t}\n\t\t\t\t\t\tuni.setStorageSync('menuInfo', menuInfo)\n\t\t\t\t\t},\n\t\t\t\t\tfail: (error) => {\n\t\t\t\t\t\tconsole.log(error)\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\t\t\t// 初始化应用\n\t\t\tinitApp() {\n\t\t\t\t// 初始化应用配置\n\t\t\t\tthis.initConfig()\n\t\t\t\t// 检查用户登录状态\n\t\t\t\t//#ifdef H5\n\t\t\t\tthis.checkLogin()\n\t\t\t\t//#endif\n\t\t\t},\n\t\t\tinitConfig() {\n\t\t\t\tthis.globalData.config = config\n\t\t\t},\n\t\t\tcheckLogin() {\n\t\t\t\tif (!getToken()) {\n\t\t\t\t\tthis.$tab.reLaunch('/pages/index')\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\">\n\t@import '@/static/scss/index.scss'\n</style>", "import mod from \"-!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752129382560\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}