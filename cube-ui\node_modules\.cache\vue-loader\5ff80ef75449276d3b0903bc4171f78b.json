{"remainingRequest": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\src\\views\\wechat\\chrome\\index.vue?vue&type=style&index=0&id=3cc1bfc8&scoped=true&lang=css", "dependencies": [{"path": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\src\\views\\wechat\\chrome\\index.vue", "mtime": 1751906448186}, {"path": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751784287954}, {"path": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751784287582}, {"path": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751784287472}, {"path": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751784291169}, {"path": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751784287559}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouYWktbWFuYWdlbWVudC1wbGF0Zm9ybSB7DQogIG1pbi1oZWlnaHQ6IDEwMHZoOw0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmN2ZhOw0KICBwYWRkaW5nLWJvdHRvbTogMzBweDsNCn0NCg0KLnRvcC1uYXYgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmOw0KICBwYWRkaW5nOiAxNXB4IDIwcHg7DQogIGJveC1zaGFkb3c6IDAgMnB4IDEycHggMCByZ2JhKDAsIDAsIDAsIDAuMSk7DQogIG1hcmdpbi1ib3R0b206IDIwcHg7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCn0NCg0KLmxvZ28tYXJlYSB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQp9DQoNCi5sb2dvIHsNCiAgaGVpZ2h0OiAzNnB4Ow0KICBtYXJnaW4tcmlnaHQ6IDEwcHg7DQp9DQoNCi5wbGF0Zm9ybS10aXRsZSB7DQogIG1hcmdpbjogMDsNCiAgZm9udC1zaXplOiAyMHB4Ow0KICBjb2xvcjogIzMwMzEzMzsNCn0NCg0KLm1haW4tY29udGVudCB7DQogIHBhZGRpbmc6IDAgMzBweDsNCiAgd2lkdGg6IDkwJTsNCiAgbWFyZ2luOiAwIGF1dG87DQp9DQo6OnYtZGVlcCAuZWwtY29sbGFwc2UtaXRlbV9faGVhZGVyIHsNCiAgZm9udC1zaXplOiAxNnB4Ow0KICBjb2xvcjogIzMzMzsNCiAgcGFkZGluZy1sZWZ0OiAyMHB4Ow0KfQ0KLnNlY3Rpb24tdGl0bGUgew0KICBmb250LXNpemU6IDE4cHg7DQogIGNvbG9yOiAjNjA2MjY2Ow0KICBtYXJnaW4tYm90dG9tOiAxNXB4Ow0KfQ0KDQouYWktY2FyZHMgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LXdyYXA6IHdyYXA7DQogIGdhcDogMjBweDsNCiAgbWFyZ2luLWJvdHRvbTogMHB4Ow0KICBtYXJnaW4tbGVmdDogMjBweDsNCiAgbWFyZ2luLXRvcDogMTBweDsNCn0NCg0KLmFpLWNhcmQgew0KICB3aWR0aDogY2FsYygyNSUgLSAyMHB4KTsNCiAgYm94LXNpemluZzogYm9yZGVyLWJveDsNCn0NCg0KLmFpLWNhcmQtaGVhZGVyIHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBtYXJnaW4tYm90dG9tOiAxNXB4Ow0KfQ0KDQouYWktbGVmdCB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQp9DQoNCi5haS1hdmF0YXIgew0KICBtYXJnaW4tcmlnaHQ6IDEwcHg7DQp9DQoNCi5haS1hdmF0YXIgaW1nIHsNCiAgd2lkdGg6IDMwcHg7DQogIGhlaWdodDogMzBweDsNCiAgYm9yZGVyLXJhZGl1czogNTAlOw0KICBvYmplY3QtZml0OiBjb3ZlcjsNCn0NCg0KLmFpLW5hbWUgew0KICBmb250LXdlaWdodDogYm9sZDsNCiAgZm9udC1zaXplOiAxMnB4Ow0KfQ0KDQouYWktc3RhdHVzIHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCn0NCg0KLmFpLWNhcGFiaWxpdGllcyB7DQogIG1hcmdpbjogMTVweCAwOw0KICB3aWR0aDogMTAwJTsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogIGZsZXgtd3JhcDogd3JhcDsNCn0NCg0KLmJ1dHRvbi1jYXBhYmlsaXR5LWdyb3VwIHsNCiAgZGlzcGxheTogZmxleDsNCiAgZmxleC13cmFwOiB3cmFwOw0KICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgZ2FwOiA4cHg7DQp9DQoNCi5idXR0b24tY2FwYWJpbGl0eS1ncm91cCAuZWwtYnV0dG9uIHsNCiAgbWFyZ2luOiAwOw0KICBib3JkZXItcmFkaXVzOiAxNnB4Ow0KICBwYWRkaW5nOiA2cHggMTJweDsNCn0NCg0KLmJ1dHRvbi1jYXBhYmlsaXR5LWdyb3VwIC5lbC1idXR0b24uaXMtcGxhaW46aG92ZXIsDQouYnV0dG9uLWNhcGFiaWxpdHktZ3JvdXAgLmVsLWJ1dHRvbi5pcy1wbGFpbjpmb2N1cyB7DQogIGJhY2tncm91bmQ6ICNlY2Y1ZmY7DQogIGJvcmRlci1jb2xvcjogI2IzZDhmZjsNCiAgY29sb3I6ICM0MDlFRkY7DQp9DQoNCi5wcm9tcHQtaW5wdXQtc2VjdGlvbiB7DQogIG1hcmdpbi1ib3R0b206IDMwcHg7DQogIHBhZGRpbmc6IDAgMjBweCAwIDBweDsNCn0NCg0KLnByb21wdC1pbnB1dCB7DQogIG1hcmdpbi1ib3R0b206IDEwcHg7DQogIG1hcmdpbi1sZWZ0OiAyMHB4Ow0KICB3aWR0aDogOTklOw0KfQ0KDQoucHJvbXB0LWZvb3RlciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIG1hcmdpbi1ib3R0b206IC0zMHB4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQp9DQoNCi53b3JkLWNvdW50IHsNCiAgZm9udC1zaXplOiAxMnB4Ow0KICBwYWRkaW5nLWxlZnQ6IDIwcHg7DQp9DQoNCi5zZW5kLWJ1dHRvbiB7DQogIHBhZGRpbmc6IDEwcHggMjBweDsNCn0NCg0KLmV4ZWN1dGlvbi1zdGF0dXMtc2VjdGlvbiB7DQogIG1hcmdpbi1ib3R0b206IDMwcHg7DQogIHBhZGRpbmc6MjBweCAwcHggMHB4IDBweDsNCn0NCg0KLnRhc2stZmxvdy1jYXJkLCAuc2NyZWVuc2hvdHMtY2FyZCB7DQogIGhlaWdodDogODAwcHg7DQp9DQoNCi5jYXJkLWhlYWRlciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCn0NCg0KLnRhc2stZmxvdyB7DQogIHBhZGRpbmc6IDE1cHg7DQogIGhlaWdodDogODAwcHg7DQogIG92ZXJmbG93LXk6IGF1dG87DQogIGJhY2tncm91bmQtY29sb3I6ICNmNWY3ZmE7DQogIGJvcmRlci1yYWRpdXM6IDRweDsNCn0NCg0KLnRhc2stZmxvdzo6LXdlYmtpdC1zY3JvbGxiYXIgew0KICB3aWR0aDogNnB4Ow0KfQ0KDQoudGFzay1mbG93Ojotd2Via2l0LXNjcm9sbGJhci10aHVtYiB7DQogIGJhY2tncm91bmQtY29sb3I6ICNjMGM0Y2M7DQogIGJvcmRlci1yYWRpdXM6IDNweDsNCn0NCg0KLnRhc2stZmxvdzo6LXdlYmtpdC1zY3JvbGxiYXItdHJhY2sgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmN2ZhOw0KfQ0KDQoudGFzay1pdGVtIHsNCiAgbWFyZ2luLWJvdHRvbTogMTVweDsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmOw0KICBib3gtc2hhZG93OiAwIDJweCA0cHggcmdiYSgwLCAwLCAwLCAwLjEpOw0KICBvdmVyZmxvdzogaGlkZGVuOw0KfQ0KDQoudGFzay1oZWFkZXIgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIHBhZGRpbmc6IDEycHggMTVweDsNCiAgY3Vyc29yOiBwb2ludGVyOw0KICB0cmFuc2l0aW9uOiBiYWNrZ3JvdW5kLWNvbG9yIDAuM3M7DQogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZWJlZWY1Ow0KfQ0KDQoudGFzay1oZWFkZXI6aG92ZXIgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmN2ZhOw0KfQ0KDQouaGVhZGVyLWxlZnQgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBnYXA6IDhweDsNCn0NCg0KLmhlYWRlci1sZWZ0IC5lbC1pY29uLWFycm93LXJpZ2h0IHsNCiAgdHJhbnNpdGlvbjogdHJhbnNmb3JtIDAuM3M7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgY29sb3I6ICM5MDkzOTk7DQp9DQoNCi5oZWFkZXItbGVmdCAuZWwtaWNvbi1hcnJvdy1yaWdodC5pcy1leHBhbmRlZCB7DQogIHRyYW5zZm9ybTogcm90YXRlKDkwZGVnKTsNCn0NCg0KLnByb2dyZXNzLXRpbWVsaW5lIHsNCiAgcG9zaXRpb246IHJlbGF0aXZlOw0KICBtYXJnaW46IDA7DQogIHBhZGRpbmc6IDE1cHggMDsNCn0NCg0KLnRpbWVsaW5lLXNjcm9sbCB7DQogIG1heC1oZWlnaHQ6IDIwMHB4Ow0KICBvdmVyZmxvdy15OiBhdXRvOw0KICBwYWRkaW5nOiAwIDE1cHg7DQp9DQoNCi50aW1lbGluZS1zY3JvbGw6Oi13ZWJraXQtc2Nyb2xsYmFyIHsNCiAgd2lkdGg6IDRweDsNCn0NCg0KLnRpbWVsaW5lLXNjcm9sbDo6LXdlYmtpdC1zY3JvbGxiYXItdGh1bWIgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjYzBjNGNjOw0KICBib3JkZXItcmFkaXVzOiAycHg7DQp9DQoNCi50aW1lbGluZS1zY3JvbGw6Oi13ZWJraXQtc2Nyb2xsYmFyLXRyYWNrIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjdmYTsNCn0NCg0KLnByb2dyZXNzLWl0ZW0gew0KICBwb3NpdGlvbjogcmVsYXRpdmU7DQogIHBhZGRpbmc6IDhweCAwIDhweCAyMHB4Ow0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogZmxleC1zdGFydDsNCiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNmMGYwZjA7DQp9DQoNCi5wcm9ncmVzcy1pdGVtOmxhc3QtY2hpbGQgew0KICBib3JkZXItYm90dG9tOiBub25lOw0KfQ0KDQoucHJvZ3Jlc3MtZG90IHsNCiAgcG9zaXRpb246IGFic29sdXRlOw0KICBsZWZ0OiAwOw0KICB0b3A6IDEycHg7DQogIHdpZHRoOiAxMHB4Ow0KICBoZWlnaHQ6IDEwcHg7DQogIGJvcmRlci1yYWRpdXM6IDUwJTsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2UwZTBlMDsNCiAgZmxleC1zaHJpbms6IDA7DQp9DQoNCi5wcm9ncmVzcy1saW5lIHsNCiAgcG9zaXRpb246IGFic29sdXRlOw0KICBsZWZ0OiA0cHg7DQogIHRvcDogMjJweDsNCiAgYm90dG9tOiAtOHB4Ow0KICB3aWR0aDogMnB4Ow0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZTBlMGUwOw0KfQ0KDQoucHJvZ3Jlc3MtY29udGVudCB7DQogIGZsZXg6IDE7DQogIG1pbi13aWR0aDogMDsNCn0NCg0KLnByb2dyZXNzLXRpbWUgew0KICBmb250LXNpemU6IDEycHg7DQogIGNvbG9yOiAjOTA5Mzk5Ow0KICBtYXJnaW4tYm90dG9tOiA0cHg7DQp9DQoNCi5wcm9ncmVzcy10ZXh0IHsNCiAgZm9udC1zaXplOiAxM3B4Ow0KICBjb2xvcjogIzYwNjI2NjsNCiAgbGluZS1oZWlnaHQ6IDEuNDsNCiAgd29yZC1icmVhazogYnJlYWstYWxsOw0KfQ0KDQoucHJvZ3Jlc3MtaXRlbS5jb21wbGV0ZWQgLnByb2dyZXNzLWRvdCB7DQogIGJhY2tncm91bmQtY29sb3I6ICM2N2MyM2E7DQp9DQoNCi5wcm9ncmVzcy1pdGVtLmNvbXBsZXRlZCAucHJvZ3Jlc3MtbGluZSB7DQogIGJhY2tncm91bmQtY29sb3I6ICM2N2MyM2E7DQp9DQoNCi5wcm9ncmVzcy1pdGVtLmN1cnJlbnQgLnByb2dyZXNzLWRvdCB7DQogIGJhY2tncm91bmQtY29sb3I6ICM0MDllZmY7DQogIGFuaW1hdGlvbjogcHVsc2UgMS41cyBpbmZpbml0ZTsNCn0NCg0KLnByb2dyZXNzLWl0ZW0uY3VycmVudCAucHJvZ3Jlc3MtbGluZSB7DQogIGJhY2tncm91bmQtY29sb3I6ICM0MDllZmY7DQp9DQoNCi5haS1uYW1lIHsNCiAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBjb2xvcjogIzMwMzEzMzsNCn0NCg0KLmhlYWRlci1yaWdodCB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGdhcDogOHB4Ow0KfQ0KDQouc3RhdHVzLXRleHQgew0KICBmb250LXNpemU6IDEzcHg7DQogIGNvbG9yOiAjNjA2MjY2Ow0KfQ0KDQouc3RhdHVzLWljb24gew0KICBmb250LXNpemU6IDE2cHg7DQp9DQoNCi5zdWNjZXNzLWljb24gew0KICBjb2xvcjogIzY3YzIzYTsNCn0NCg0KLmVycm9yLWljb24gew0KICBjb2xvcjogI2Y1NmM2YzsNCn0NCg0KQGtleWZyYW1lcyBwdWxzZSB7DQogIDAlIHsNCiAgICBib3gtc2hhZG93OiAwIDAgMCAwIHJnYmEoNjQsIDE1OCwgMjU1LCAwLjQpOw0KICB9DQogIDcwJSB7DQogICAgYm94LXNoYWRvdzogMCAwIDAgNnB4IHJnYmEoNjQsIDE1OCwgMjU1LCAwKTsNCiAgfQ0KICAxMDAlIHsNCiAgICBib3gtc2hhZG93OiAwIDAgMCAwIHJnYmEoNjQsIDE1OCwgMjU1LCAwKTsNCiAgfQ0KfQ0KDQouc2NyZWVuc2hvdC1pbWFnZSB7DQogIHdpZHRoOiAxMDAlOw0KICBoZWlnaHQ6IDEwMCU7DQogIG9iamVjdC1maXQ6IGNvbnRhaW47DQogIGN1cnNvcjogcG9pbnRlcjsNCiAgdHJhbnNpdGlvbjogdHJhbnNmb3JtIDAuM3M7DQp9DQoNCi5zY3JlZW5zaG90LWltYWdlOmhvdmVyIHsNCiAgdHJhbnNmb3JtOiBzY2FsZSgxLjA1KTsNCn0NCg0KLnJlc3VsdHMtc2VjdGlvbiB7DQogIG1hcmdpbi10b3A6IDIwcHg7DQogIHBhZGRpbmc6IDAgMTBweDsNCn0NCg0KLnJlc3VsdC1jb250ZW50IHsNCiAgcGFkZGluZzogMjBweCAzMHB4Ow0KfQ0KDQoucmVzdWx0LWhlYWRlciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgbWFyZ2luLWJvdHRvbTogMTVweDsNCiAgcGFkZGluZy1ib3R0b206IDEwcHg7DQogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZWJlZWY1Ow0KfQ0KDQoucmVzdWx0LXRpdGxlIHsNCiAgZm9udC1zaXplOiAxNnB4Ow0KICBmb250LXdlaWdodDogNjAwOw0KICBjb2xvcjogIzMwMzEzMzsNCn0NCg0KLnJlc3VsdC1idXR0b25zIHsNCiAgZGlzcGxheTogZmxleDsNCiAgZ2FwOiAxMHB4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KfQ0KDQouc2hhcmUtbGluay1idG4sIC5wdXNoLXdlY2hhdC1idG4gew0KICBib3JkZXItcmFkaXVzOiAxNnB4Ow0KICBwYWRkaW5nOiA2cHggMTJweDsNCn0NCg0KLm1hcmtkb3duLWNvbnRlbnQgew0KICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KICBtYXgtaGVpZ2h0OiA0MDBweDsNCiAgb3ZlcmZsb3cteTogYXV0bzsNCiAgcGFkZGluZzogMTVweCAyMHB4Ow0KICBib3JkZXI6IDFweCBzb2xpZCAjZWJlZWY1Ow0KICBib3JkZXItcmFkaXVzOiA0cHg7DQogIGJhY2tncm91bmQtY29sb3I6ICNmZmY7DQp9DQoNCi5hY3Rpb24tYnV0dG9ucyB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogZmxleC1lbmQ7DQogIGdhcDogMTBweDsNCiAgcGFkZGluZzogMCAxMHB4Ow0KfQ0KDQpAbWVkaWEgKG1heC13aWR0aDogMTIwMHB4KSB7DQogIC5haS1jYXJkIHsNCiAgICB3aWR0aDogY2FsYygzMy4zMyUgLSAxNHB4KTsNCiAgfQ0KfQ0KDQpAbWVkaWEgKG1heC13aWR0aDogOTkycHgpIHsNCiAgLmFpLWNhcmQgew0KICAgIHdpZHRoOiBjYWxjKDUwJSAtIDEwcHgpOw0KICB9DQp9DQoNCkBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkgew0KICAuYWktY2FyZCB7DQogICAgd2lkdGg6IDEwMCU7DQogIH0NCn0NCg0KLmVsLWNvbGxhcHNlIHsNCiAgYm9yZGVyLXRvcDogbm9uZTsNCiAgYm9yZGVyLWJvdHRvbTogbm9uZTsNCn0NCg0KDQoNCi5lbC1jb2xsYXBzZS1pdGVtX19jb250ZW50IHsNCiAgcGFkZGluZzogMTVweCAwOw0KfQ0KDQouYWktc2VsZWN0aW9uLXNlY3Rpb24gew0KICBtYXJnaW4tYm90dG9tOiAwOw0KfQ0KDQoucHJvbXB0LWlucHV0LXNlY3Rpb24gew0KICBtYXJnaW4tYm90dG9tOiAzMHB4Ow0KICBwYWRkaW5nOiAwIDIwcHggMCAwcHg7DQp9DQoNCi5pbWFnZS1kaWFsb2cgLmVsLWRpYWxvZ19fYm9keSB7DQogIHBhZGRpbmc6IDA7DQp9DQoNCi5sYXJnZS1pbWFnZS1jb250YWluZXIgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgYmFja2dyb3VuZC1jb2xvcjogIzAwMDsNCn0NCg0KLmxhcmdlLWltYWdlIHsNCiAgbWF4LXdpZHRoOiAxMDAlOw0KICBtYXgtaGVpZ2h0OiA4MHZoOw0KICBvYmplY3QtZml0OiBjb250YWluOw0KfQ0KDQouaW1hZ2UtZGlhbG9nIC5lbC1jYXJvdXNlbCB7DQogIHdpZHRoOiAxMDAlOw0KICBoZWlnaHQ6IDEwMCU7DQp9DQoNCi5pbWFnZS1kaWFsb2cgLmVsLWNhcm91c2VsX19jb250YWluZXIgew0KICBoZWlnaHQ6IDgwdmg7DQp9DQoNCi5pbWFnZS1kaWFsb2cgLmVsLWNhcm91c2VsX19pdGVtIHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGJhY2tncm91bmQtY29sb3I6ICMwMDA7DQp9DQoNCi5zZWN0aW9uLWhlYWRlciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgbWFyZ2luLWJvdHRvbTogMTVweDsNCn0NCg0KLnNjb3JlLWRpYWxvZy1jb250ZW50IHsNCiAgcGFkZGluZzogMjBweDsNCn0NCg0KLnNlbGVjdGVkLXJlc3VsdHMgew0KICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KfQ0KDQoucmVzdWx0LWNoZWNrYm94IHsNCiAgbWFyZ2luLXJpZ2h0OiAyMHB4Ow0KICBtYXJnaW4tYm90dG9tOiAxMHB4Ow0KfQ0KDQouc2NvcmUtcHJvbXB0LXNlY3Rpb24gew0KICBtYXJnaW4tdG9wOiAyMHB4Ow0KfQ0KDQouc2NvcmUtcHJvbXB0LWlucHV0IHsNCiAgbWFyZ2luLXRvcDogMTBweDsNCn0NCg0KLnNjb3JlLXByb21wdC1pbnB1dCAuZWwtdGV4dGFyZWFfX2lubmVyIHsNCiAgbWluLWhlaWdodDogNTAwcHggIWltcG9ydGFudDsNCn0NCg0KLmRpYWxvZy1mb290ZXIgew0KICB0ZXh0LWFsaWduOiByaWdodDsNCn0NCg0KLnNjb3JlLWRpYWxvZyAuZWwtZGlhbG9nIHsNCiAgaGVpZ2h0OiA5NXZoOw0KICBtYXJnaW4tdG9wOiAyLjV2aCAhaW1wb3J0YW50Ow0KfQ0KDQouc2NvcmUtZGlhbG9nIC5lbC1kaWFsb2dfX2JvZHkgew0KICBoZWlnaHQ6IGNhbGMoOTV2aCAtIDEyMHB4KTsNCiAgb3ZlcmZsb3cteTogYXV0bzsNCiAgcGFkZGluZzogMjBweDsNCn0NCg0KLm5hdi1idXR0b25zIHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgZ2FwOiAyMHB4Ow0KfQ0KDQouaGlzdG9yeS1idXR0b24gew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KfQ0KDQouaGlzdG9yeS1pY29uIHsNCiAgd2lkdGg6IDI0cHg7DQogIGhlaWdodDogMjRweDsNCiAgdmVydGljYWwtYWxpZ246IG1pZGRsZTsNCn0NCg0KLmhpc3RvcnktY29udGVudCB7DQogIHBhZGRpbmc6IDIwcHg7DQp9DQoNCi5oaXN0b3J5LWdyb3VwIHsNCiAgbWFyZ2luLWJvdHRvbTogMjBweDsNCn0NCg0KLmhpc3RvcnktZGF0ZSB7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgY29sb3I6ICM5MDkzOTk7DQogIG1hcmdpbi1ib3R0b206IDEwcHg7DQogIHBhZGRpbmc6IDVweCAwOw0KICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2ViZWVmNTsNCn0NCg0KLmhpc3RvcnktbGlzdCB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogIGdhcDogMTBweDsNCn0NCg0KLmhpc3RvcnktaXRlbSB7DQogIG1hcmdpbi1ib3R0b206IDE1cHg7DQogIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjdmYTsNCiAgb3ZlcmZsb3c6IGhpZGRlbjsNCn0NCg0KLmhpc3RvcnktcGFyZW50IHsNCiAgcGFkZGluZzogMTBweDsNCiAgY3Vyc29yOiBwb2ludGVyOw0KICB0cmFuc2l0aW9uOiBiYWNrZ3JvdW5kLWNvbG9yIDAuM3M7DQogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZWJlZWY1Ow0KfQ0KDQouaGlzdG9yeS1wYXJlbnQ6aG92ZXIgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZWNmNWZmOw0KfQ0KDQouaGlzdG9yeS1jaGlsZHJlbiB7DQogIHBhZGRpbmctbGVmdDogMjBweDsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjsNCiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTsNCn0NCg0KLmhpc3RvcnktY2hpbGQtaXRlbSB7DQogIHBhZGRpbmc6IDhweCAxMHB4Ow0KICBjdXJzb3I6IHBvaW50ZXI7DQogIHRyYW5zaXRpb246IGJhY2tncm91bmQtY29sb3IgMC4zczsNCiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNmMGYwZjA7DQp9DQoNCi5oaXN0b3J5LWNoaWxkLWl0ZW06bGFzdC1jaGlsZCB7DQogIGJvcmRlci1ib3R0b206IG5vbmU7DQp9DQoNCi5oaXN0b3J5LWNoaWxkLWl0ZW06aG92ZXIgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmN2ZhOw0KfQ0KDQouaGlzdG9yeS1oZWFkZXIgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogZmxleC1zdGFydDsNCiAgZ2FwOiA4cHg7DQp9DQoNCi5oaXN0b3J5LWhlYWRlciAuZWwtaWNvbi1hcnJvdy1yaWdodCB7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgY29sb3I6ICM5MDkzOTk7DQogIHRyYW5zaXRpb246IHRyYW5zZm9ybSAwLjNzOw0KICBjdXJzb3I6IHBvaW50ZXI7DQogIG1hcmdpbi10b3A6IDNweDsNCn0NCg0KLmhpc3RvcnktaGVhZGVyIC5lbC1pY29uLWFycm93LXJpZ2h0LmlzLWV4cGFuZGVkIHsNCiAgdHJhbnNmb3JtOiByb3RhdGUoOTBkZWcpOw0KfQ0KDQouaGlzdG9yeS1wcm9tcHQgew0KICBmb250LXNpemU6IDE0cHg7DQogIGNvbG9yOiAjMzAzMTMzOw0KICBtYXJnaW4tYm90dG9tOiA1cHg7DQogIG92ZXJmbG93OiBoaWRkZW47DQogIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzOw0KICBkaXNwbGF5OiAtd2Via2l0LWJveDsNCiAgLXdlYmtpdC1saW5lLWNsYW1wOiAyOw0KICAtd2Via2l0LWJveC1vcmllbnQ6IHZlcnRpY2FsOw0KICBmbGV4OiAxOw0KfQ0KDQouaGlzdG9yeS10aW1lIHsNCiAgZm9udC1zaXplOiAxMnB4Ow0KICBjb2xvcjogIzkwOTM5OTsNCn0NCg0KLmNhcGFiaWxpdHktYnV0dG9uIHsNCiAgdHJhbnNpdGlvbjogYWxsIDAuM3M7DQp9DQoNCi5jYXBhYmlsaXR5LWJ1dHRvbi5lbC1idXR0b24tLXByaW1hcnkgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjNDA5RUZGOw0KICBib3JkZXItY29sb3I6ICM0MDlFRkY7DQogIGNvbG9yOiAjZmZmOw0KfQ0KDQouY2FwYWJpbGl0eS1idXR0b24uZWwtYnV0dG9uLS1pbmZvIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjsNCiAgYm9yZGVyLWNvbG9yOiAjZGNkZmU2Ow0KICBjb2xvcjogIzYwNjI2NjsNCn0NCg0KLmNhcGFiaWxpdHktYnV0dG9uLmVsLWJ1dHRvbi0taW5mbzpob3ZlciB7DQogIGNvbG9yOiAjNDA5RUZGOw0KICBib3JkZXItY29sb3I6ICNjNmUyZmY7DQogIGJhY2tncm91bmQtY29sb3I6ICNlY2Y1ZmY7DQp9DQoNCi5jYXBhYmlsaXR5LWJ1dHRvbi5lbC1idXR0b24tLXByaW1hcnk6aG92ZXIgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjNjZiMWZmOw0KICBib3JkZXItY29sb3I6ICM2NmIxZmY7DQogIGNvbG9yOiAjZmZmOw0KfQ0KDQovKiDliIbkuqvlhoXlrrnmoLflvI8gKi8NCi5zaGFyZS1jb250ZW50IHsNCiAgbWFyZ2luLWJvdHRvbTogMjBweDsNCiAgcGFkZGluZzogMTVweCAyMHB4Ow0KICBib3JkZXI6IDFweCBzb2xpZCAjZWJlZWY1Ow0KICBib3JkZXItcmFkaXVzOiA0cHg7DQogIGJhY2tncm91bmQtY29sb3I6ICNmZmY7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICBhbGlnbi1pdGVtczogZmxleC1zdGFydDsNCiAgbWluLWhlaWdodDogNjAwcHg7DQogIG1heC1oZWlnaHQ6IDgwMHB4Ow0KICBvdmVyZmxvdzogYXV0bzsNCn0NCg0KLnNoYXJlLWltYWdlIHsNCiAgb2JqZWN0LWZpdDogY29udGFpbjsNCiAgZGlzcGxheTogYmxvY2s7DQp9DQoNCi5zaGFyZS1wZGYgew0KICB3aWR0aDogMTAwJTsNCiAgaGVpZ2h0OiA2MDBweDsNCiAgYm9yZGVyOiBub25lOw0KICBib3JkZXItcmFkaXVzOiA0cHg7DQp9DQoNCi5zaGFyZS1maWxlIHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGhlaWdodDogMjAwcHg7DQogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogIGNvbG9yOiAjOTA5Mzk5Ow0KfQ0KDQouc2luZ2xlLWltYWdlLWNvbnRhaW5lciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICB3aWR0aDogMTAwJTsNCiAgaGVpZ2h0OiA4MHZoOw0KfQ0KDQouc2luZ2xlLWltYWdlLWNvbnRhaW5lciAubGFyZ2UtaW1hZ2Ugew0KICBtYXgtd2lkdGg6IDEwMCU7DQogIG1heC1oZWlnaHQ6IDEwMCU7DQogIG9iamVjdC1maXQ6IGNvbnRhaW47DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoxCA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/wechat/chrome", "sourcesContent": ["<template>\r\n  <div class=\"ai-management-platform\">\r\n    <!-- 顶部导航区 -->\r\n    <div class=\"top-nav\">\r\n      <div class=\"logo-area\">\r\n        <img src=\"../../../assets/ai/logo.png\" alt=\"Logo\" class=\"logo\">\r\n        <h1 class=\"platform-title\">主机</h1>\r\n      </div>\r\n      <div class=\"nav-buttons\">\r\n        <el-button type=\"primary\" size=\"small\" @click=\"createNewChat\">\r\n          <i class=\"el-icon-plus\"></i>\r\n          创建新对话\r\n        </el-button>\r\n        <div class=\"history-button\">\r\n          <el-button type=\"text\" @click=\"showHistoryDrawer\">\r\n            <img :src=\"require('../../../assets/ai/celan.png')\" alt=\"历史记录\" class=\"history-icon\">\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 历史记录抽屉 -->\r\n    <el-drawer title=\"历史会话记录\" :visible.sync=\"historyDrawerVisible\" direction=\"rtl\" size=\"30%\"\r\n      :before-close=\"handleHistoryDrawerClose\">\r\n      <div class=\"history-content\">\r\n        <div v-for=\"(group, date) in groupedHistory\" :key=\"date\" class=\"history-group\">\r\n          <div class=\"history-date\">{{ date }}</div>\r\n          <div class=\"history-list\">\r\n            <div v-for=\"(item, index) in group\" :key=\"index\" class=\"history-item\">\r\n              <div class=\"history-parent\" @click=\"loadHistoryItem(item)\">\r\n                <div class=\"history-header\">\r\n                  <i :class=\"['el-icon-arrow-right', {'is-expanded': item.isExpanded}]\"\r\n                    @click.stop=\"toggleHistoryExpansion(item)\"></i>\r\n                  <div class=\"history-prompt\">{{ item.userPrompt }}</div>\r\n                </div>\r\n                <div class=\"history-time\">{{ formatHistoryTime(item.createTime) }}</div>\r\n              </div>\r\n              <div v-if=\"item.children && item.children.length > 0 && item.isExpanded\" class=\"history-children\">\r\n                <div v-for=\"(child, childIndex) in item.children\" :key=\"childIndex\" class=\"history-child-item\"\r\n                  @click=\"loadHistoryItem(child)\">\r\n                  <div class=\"history-prompt\">{{ child.userPrompt }}</div>\r\n                  <div class=\"history-time\">{{ formatHistoryTime(child.createTime) }}</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-drawer>\r\n\r\n    <div class=\"main-content\">\r\n      <el-collapse v-model=\"activeCollapses\">\r\n        <el-collapse-item title=\"AI选择配置\" name=\"ai-selection\">\r\n          <div class=\"ai-selection-section\">\r\n            <div class=\"ai-cards\">\r\n              <el-card v-for=\"(ai, index) in aiList\" :key=\"index\" class=\"ai-card\" shadow=\"hover\">\r\n                <div class=\"ai-card-header\">\r\n                  <div class=\"ai-left\">\r\n                    <div class=\"ai-avatar\">\r\n                      <img :src=\"ai.avatar\" alt=\"AI头像\">\r\n                    </div>\r\n                    <div class=\"ai-name\">{{ ai.name }}</div>\r\n                  </div>\r\n                  <div class=\"ai-status\">\r\n                    <el-switch v-model=\"ai.enabled\" active-color=\"#13ce66\" inactive-color=\"#ff4949\">\r\n                    </el-switch>\r\n                  </div>\r\n                </div>\r\n                <div class=\"ai-capabilities\" v-if=\"ai.capabilities && ai.capabilities.length > 0\">\r\n                  <div class=\"button-capability-group\">\r\n                    <el-button v-for=\"capability in ai.capabilities\" :key=\"capability.value\" size=\"mini\"\r\n                      :type=\"ai.selectedCapabilities.includes(capability.value) ? 'primary' : 'info'\"\r\n                      :disabled=\"!ai.enabled\" :plain=\"!ai.selectedCapabilities.includes(capability.value)\"\r\n                      @click=\"toggleCapability(ai, capability.value)\" class=\"capability-button\">\r\n                      {{ capability.label }}\r\n                    </el-button>\r\n                  </div>\r\n                </div>\r\n              </el-card>\r\n            </div>\r\n          </div>\r\n        </el-collapse-item>\r\n\r\n        <!-- 提示词输入区 -->\r\n        <el-collapse-item title=\"提示词输入\" name=\"prompt-input\">\r\n          <div class=\"prompt-input-section\">\r\n            <el-input type=\"textarea\" :rows=\"5\" placeholder=\"请输入提示词，支持Markdown格式\" v-model=\"promptInput\" resize=\"none\"\r\n              class=\"prompt-input\">\r\n            </el-input>\r\n            <div class=\"prompt-footer\">\r\n              <div class=\"word-count\">字数统计: {{ promptInput.length }}</div>\r\n              <el-button type=\"primary\" @click=\"sendPrompt\" :disabled=\"!canSend\" class=\"send-button\">\r\n                发送\r\n              </el-button>\r\n            </div>\r\n          </div>\r\n        </el-collapse-item>\r\n      </el-collapse>\r\n\r\n      <!-- 执行状态展示区 -->\r\n      <div class=\"execution-status-section\" v-if=\"taskStarted\">\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-card class=\"task-flow-card\">\r\n              <div slot=\"header\" class=\"card-header\">\r\n                <span>任务流程</span>\r\n              </div>\r\n              <div class=\"task-flow\">\r\n                <div v-for=\"(ai, index) in enabledAIs\" :key=\"index\" class=\"task-item\">\r\n                  <div class=\"task-header\" @click=\"toggleAIExpansion(ai)\">\r\n                    <div class=\"header-left\">\r\n                      <i :class=\"['el-icon-arrow-right', {'is-expanded': ai.isExpanded}]\"></i>\r\n                      <span class=\"ai-name\">{{ ai.name }}</span>\r\n                    </div>\r\n                    <div class=\"header-right\">\r\n                      <span class=\"status-text\">{{ getStatusText(ai.status) }}</span>\r\n                      <i :class=\"getStatusIcon(ai.status)\" class=\"status-icon\"></i>\r\n                    </div>\r\n                  </div>\r\n                  <!-- 添加进度轨迹 -->\r\n                  <div class=\"progress-timeline\" v-if=\"ai.progressLogs.length > 0 && ai.isExpanded\">\r\n                    <div class=\"timeline-scroll\">\r\n                      <div v-for=\"(log, logIndex) in ai.progressLogs\" :key=\"logIndex\" class=\"progress-item\" :class=\"{\r\n                             'completed': log.isCompleted || logIndex > 0,\r\n                             'current': !log.isCompleted && logIndex === 0\r\n                           }\">\r\n                        <div class=\"progress-dot\"></div>\r\n                        <div class=\"progress-line\" v-if=\"logIndex < ai.progressLogs.length - 1\"></div>\r\n                        <div class=\"progress-content\">\r\n                          <div class=\"progress-time\">{{ formatTime(log.timestamp) }}</div>\r\n                          <div class=\"progress-text\">{{ log.content }}</div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-card class=\"screenshots-card\">\r\n              <div slot=\"header\" class=\"card-header\">\r\n                <span>主机可视化</span>\r\n                <div class=\"controls\">\r\n                  <el-switch v-model=\"autoPlay\" active-text=\"自动轮播\" inactive-text=\"手动切换\">\r\n                  </el-switch>\r\n                </div>\r\n              </div>\r\n              <div class=\"screenshots\">\r\n                <el-carousel :interval=\"3000\" :autoplay=\"false\" indicator-position=\"outside\" height=\"700px\">\r\n                  <el-carousel-item v-for=\"(screenshot, index) in screenshots\" :key=\"index\">\r\n                    <img :src=\"screenshot\" alt=\"执行截图\" class=\"screenshot-image\" @click=\"showLargeImage(screenshot)\">\r\n                  </el-carousel-item>\r\n                </el-carousel>\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n        </el-row>\r\n      </div>\r\n\r\n      <!-- 结果展示区 -->\r\n      <div class=\"results-section\" v-if=\"results.length > 0\">\r\n        <div class=\"section-header\">\r\n          <h2 class=\"section-title\">执行结果</h2>\r\n          <el-button type=\"primary\" @click=\"showScoreDialog\" size=\"small\">\r\n            智能评分\r\n          </el-button>\r\n        </div>\r\n        <el-tabs v-model=\"activeResultTab\" type=\"card\">\r\n          <el-tab-pane v-for=\"(result, index) in results\" :key=\"index\" :label=\"result.aiName\" :name=\"'result-' + index\">\r\n            <div class=\"result-content\">\r\n              <div class=\"result-header\" v-if=\"result.shareUrl\">\r\n                <div class=\"result-title\">{{ result.aiName }}的执行结果</div>\r\n                <div class=\"result-buttons\">\r\n                  <el-button size=\"mini\" type=\"primary\" icon=\"el-icon-link\" @click=\"openShareUrl(result.shareUrl)\"\r\n                    class=\"share-link-btn\">\r\n                    查看原链接\r\n                  </el-button>\r\n                  <el-button size=\"mini\" type=\"success\" icon=\"el-icon-s-promotion\" @click=\"handlePushToWechat(result)\"\r\n                    class=\"push-wechat-btn\" :loading=\"pushingToWechat\" :disabled=\"pushingToWechat\">\r\n                    投递到公众号\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n              <!-- 如果有shareImgUrl则渲染图片或PDF，否则渲染markdown -->\r\n              <div v-if=\"result.shareImgUrl\" class=\"share-content\">\r\n                <!-- 渲染图片 -->\r\n                <img v-if=\"isImageFile(result.shareImgUrl)\" :src=\"result.shareImgUrl\" alt=\"分享图片\" class=\"share-image\"\r\n                  :style=\"getImageStyle(result.aiName)\">\r\n                <!-- 渲染PDF -->\r\n                <iframe v-else-if=\"isPdfFile(result.shareImgUrl)\" :src=\"result.shareImgUrl\" class=\"share-pdf\"\r\n                  frameborder=\"0\">\r\n                </iframe>\r\n                <!-- 其他文件类型显示链接 -->\r\n                <div v-else class=\"share-file\">\r\n                  <el-button type=\"primary\" icon=\"el-icon-document\" @click=\"openShareUrl(result.shareImgUrl)\">\r\n                    查看文件\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n              <div v-else class=\"markdown-content\" v-html=\"renderMarkdown(result.content)\"></div>\r\n              <div class=\"action-buttons\">\r\n                <el-button size=\"small\" type=\"primary\" @click=\"copyResult(result.content)\">复制（纯文本）</el-button>\r\n                <el-button size=\"small\" type=\"success\" @click=\"exportResult(result)\">导出（MD文件）</el-button>\r\n              </div>\r\n            </div>\r\n          </el-tab-pane>\r\n        </el-tabs>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 大图查看对话框 -->\r\n    <el-dialog :visible.sync=\"showImageDialog\" width=\"90%\" :show-close=\"true\" :modal=\"true\" center class=\"image-dialog\"\r\n      :append-to-body=\"true\" @close=\"closeLargeImage\">\r\n      <div class=\"large-image-container\">\r\n        <!-- 如果是单张分享图片，直接显示 -->\r\n        <div v-if=\"currentLargeImage && !screenshots.includes(currentLargeImage)\" class=\"single-image-container\">\r\n          <img :src=\"currentLargeImage\" alt=\"大图\" class=\"large-image\">\r\n        </div>\r\n        <!-- 如果是截图轮播 -->\r\n        <el-carousel v-else :interval=\"3000\" :autoplay=\"false\" indicator-position=\"outside\" height=\"80vh\">\r\n          <el-carousel-item v-for=\"(screenshot, index) in screenshots\" :key=\"index\">\r\n            <img :src=\"screenshot\" alt=\"大图\" class=\"large-image\">\r\n          </el-carousel-item>\r\n        </el-carousel>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 评分弹窗 -->\r\n    <el-dialog title=\"智能评分\" :visible.sync=\"scoreDialogVisible\" width=\"60%\" height=\"65%\" :close-on-click-modal=\"false\"\r\n      class=\"score-dialog\">\r\n      <div class=\"score-dialog-content\">\r\n        <div class=\"score-prompt-section\">\r\n          <h3>评分提示词：</h3>\r\n          <el-input type=\"textarea\" :rows=\"10\" placeholder=\"请输入评分提示词，例如：请从内容质量、逻辑性、创新性等方面进行评分\" v-model=\"scorePrompt\"\r\n            resize=\"none\" class=\"score-prompt-input\">\r\n          </el-input>\r\n        </div>\r\n        <div class=\"selected-results\">\r\n          <h3>选择要评分的内容：</h3>\r\n          <el-checkbox-group v-model=\"selectedResults\">\r\n            <el-checkbox v-for=\"(result, index) in results\" :key=\"index\" :label=\"result.aiName\" class=\"result-checkbox\">\r\n              {{ result.aiName }}\r\n            </el-checkbox>\r\n          </el-checkbox-group>\r\n        </div>\r\n\r\n\r\n      </div>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"scoreDialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"handleScore\" :disabled=\"!canScore\">\r\n          开始评分\r\n        </el-button>\r\n      </span>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {marked} from 'marked';\r\nimport {message, saveUserChatData, getChatHistory, pushAutoOffice} from \"@/api/wechat/aigc\";\r\nimport {\r\n\t\tv4 as uuidv4\r\n\t} from 'uuid';\r\nimport websocketClient from '@/utils/websocket';\r\nimport store from '@/store';\r\nimport TurndownService from 'turndown';\r\n\r\nexport default {\r\n  name: 'AIManagementPlatform',\r\n  data() {\r\n    return {\r\n      userId: store.state.user.id,\r\n      corpId: store.state.user.corp_id,\r\n      chatId: uuidv4(),\r\n      expandedHistoryItems: {},\r\n      userInfoReq: {\r\n        userPrompt: '',\r\n        userId: '',\r\n        corpId: '',\r\n        taskId: '',\r\n        roles: '',\r\n        toneChatId: '',\r\n        ybDsChatId: '',\r\n        dbChatId: '',\r\n        isNewChat: true\r\n      },\r\n      jsonRpcReqest: {\r\n        jsonrpc: '2.0',\r\n        id: uuidv4(),\r\n        method: '',\r\n        params: {}\r\n      },\r\n      aiList: [\r\n        {\r\n          name: 'TurboS@元器',\r\n          avatar: require('../../../assets/ai/yuanbao.png'),\r\n          capabilities: [],\r\n          selectedCapabilities: [],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        },\r\n        {\r\n          name: 'TurboS长文版@元器',\r\n          avatar: require('../../../assets/ai/yuanbao.png'),\r\n          capabilities: [],\r\n          selectedCapabilities: [],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        },\r\n        // {\r\n        //   name: 'MiniMax@元器',\r\n        //   avatar: require('../../../assets/ai/yuanbao.png'),\r\n        //   capabilities: [],\r\n        //   selectedCapabilities: [],\r\n        //   enabled: true,\r\n        //   status: 'idle',\r\n        //   progressLogs: [],\r\n        //   isExpanded: true\r\n        // },\r\n        // {\r\n        //   name: '搜狗搜索@元器',\r\n        //   avatar: require('../../../assets/ai/yuanbao.png'),\r\n        //   capabilities: [],\r\n        //   selectedCapabilities: [],\r\n        //   enabled: true,\r\n        //   status: 'idle',\r\n        //   progressLogs: [],\r\n        //   isExpanded: true\r\n        // },\r\n        // {\r\n        //   name: 'KIMI@元器',\r\n        //   avatar: require('../../../assets/ai/yuanbao.png'),\r\n        //   capabilities: [],\r\n        //   selectedCapabilities: [],\r\n        //   enabled: true,\r\n        //   status: 'idle',\r\n        //   progressLogs: [],\r\n        //   isExpanded: true\r\n        // },\r\n        {\r\n          name: '腾讯元宝T1',\r\n          avatar: require('../../../assets/ai/yuanbao.png'),\r\n          capabilities: [\r\n            { label: '深度思考', value: 'deep_thinking' },\r\n            { label: '联网搜索', value: 'web_search' }\r\n          ],\r\n          selectedCapabilities: ['deep_thinking','web_search'],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        },\r\n        {\r\n          name: '腾讯元宝DS',\r\n          avatar: require('../../../assets/ai/yuanbao.png'),\r\n          capabilities: [\r\n            { label: '深度思考', value: 'deep_thinking' },\r\n            { label: '联网搜索', value: 'web_search' }\r\n          ],\r\n          selectedCapabilities: ['deep_thinking','web_search'],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        },\r\n        {\r\n          name: '豆包',\r\n          avatar: require('../../../assets/ai/豆包.png'),\r\n          capabilities: [\r\n            { label: '深度思考', value: 'deep_thinking' }\r\n          ],\r\n          selectedCapabilities: ['deep_thinking'],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        },\r\n        {\r\n          name: '百度AI',\r\n          avatar: require('../../../assets/ai/baidu.png'),\r\n          capabilities: [],\r\n          selectedCapabilities: [],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        }\r\n      ],\r\n      promptInput: '',\r\n      taskStarted: false,\r\n      autoPlay: false,\r\n      screenshots: [],\r\n      results: [],\r\n      activeResultTab: 'result-0',\r\n      activeCollapses: ['ai-selection', 'prompt-input'], // 默认展开这两个区域\r\n      showImageDialog: false,\r\n      currentLargeImage: '',\r\n      enabledAIs: [],\r\n      turndownService: new TurndownService({\r\n        headingStyle: 'atx',\r\n        codeBlockStyle: 'fenced',\r\n        emDelimiter: '*'\r\n      }),\r\n      scoreDialogVisible: false,\r\n      selectedResults: [],\r\n      scorePrompt: `请你深度阅读以下几篇公众号章，从多个维度进行逐项打分，输出评分结果。并在以下各篇文章的基础上博采众长，综合整理一篇更全面的文章。`,\r\n      historyDrawerVisible: false,\r\n      chatHistory: [],\r\n      pushOfficeNum: 0, // 投递到公众号的递增编号\r\n      pushingToWechat: false, // 投递到公众号的loading状态\r\n    };\r\n  },\r\n  computed: {\r\n    canSend() {\r\n      return this.promptInput.trim().length > 0 && this.aiList.some(ai => ai.enabled);\r\n    },\r\n    canScore() {\r\n      return this.selectedResults.length > 0 && this.scorePrompt.trim().length > 0;\r\n    },\r\n    groupedHistory() {\r\n      const groups = {};\r\n      const chatGroups = {};\r\n\r\n      // 首先按chatId分组\r\n      this.chatHistory.forEach(item => {\r\n        if (!chatGroups[item.chatId]) {\r\n          chatGroups[item.chatId] = [];\r\n        }\r\n        chatGroups[item.chatId].push(item);\r\n      });\r\n\r\n      // 然后按日期分组，并处理父子关系\r\n      Object.values(chatGroups).forEach(chatGroup => {\r\n        // 按时间排序\r\n        chatGroup.sort((a, b) => new Date(a.createTime) - new Date(b.createTime));\r\n\r\n        // 获取最早的记录作为父级\r\n        const parentItem = chatGroup[0];\r\n        const date = this.getHistoryDate(parentItem.createTime);\r\n\r\n        if (!groups[date]) {\r\n          groups[date] = [];\r\n        }\r\n\r\n        // 添加父级记录\r\n        groups[date].push({\r\n          ...parentItem,\r\n          isParent: true,\r\n          isExpanded: this.expandedHistoryItems[parentItem.chatId] || false,\r\n          children: chatGroup.slice(1).map(child => ({\r\n            ...child,\r\n            isParent: false\r\n          }))\r\n        });\r\n      });\r\n\r\n      return groups;\r\n    }\r\n  },\r\n  created() {\r\n    console.log(this.userId);\r\n    console.log(this.corpId);\r\n    this.initWebSocket(this.userId);\r\n    this.loadChatHistory(0); // 加载历史记录\r\n    this.loadLastChat(); // 加载上次会话\r\n  },\r\n  methods: {\r\n    sendPrompt() {\r\n      if (!this.canSend) return;\r\n\r\n      this.screenshots =[];\r\n      // 折叠所有区域\r\n      this.activeCollapses = [];\r\n\r\n      this.taskStarted = true;\r\n      this.results = []; // 清空之前的结果\r\n\r\n      this.userInfoReq.roles = '';\r\n\r\n\r\n      this.userInfoReq.taskId = uuidv4();\r\n      this.userInfoReq.userId = this.userId;\r\n      this.userInfoReq.corpId = this.corpId;\r\n      this.userInfoReq.userPrompt = this.promptInput;\r\n\r\n      // 获取启用的AI列表及其状态\r\n      this.enabledAIs = this.aiList.filter(ai => ai.enabled);\r\n\r\n      // 将所有启用的AI状态设置为运行中\r\n      this.enabledAIs.forEach(ai => {\r\n        this.$set(ai, 'status', 'running');\r\n      });\r\n\r\n      this.enabledAIs.forEach(ai => {\r\n        if(ai.name === '腾讯元宝T1'){\r\n          this.userInfoReq.roles = this.userInfoReq.roles + 'yb-hunyuan-pt,';\r\n          if(ai.selectedCapabilities.includes(\"deep_thinking\")){\r\n            this.userInfoReq.roles = this.userInfoReq.roles + 'yb-hunyuan-sdsk,';\r\n          }\r\n          if(ai.selectedCapabilities.includes(\"web_search\")){\r\n            this.userInfoReq.roles = this.userInfoReq.roles + 'yb-hunyuan-lwss,';\r\n          }\r\n        }\r\n        if(ai.name === '腾讯元宝DS'){\r\n          this.userInfoReq.roles = this.userInfoReq.roles + 'yb-deepseek-pt,';\r\n          if(ai.selectedCapabilities.includes(\"deep_thinking\")){\r\n            this.userInfoReq.roles = this.userInfoReq.roles + 'yb-deepseek-sdsk,';\r\n          }\r\n          if(ai.selectedCapabilities.includes(\"web_search\")){\r\n            this.userInfoReq.roles = this.userInfoReq.roles + 'yb-deepseek-lwss,';\r\n          }\r\n        }\r\n        if(ai.name === 'TurboS@元器'){\r\n          this.userInfoReq.roles = this.userInfoReq.roles + 'cube-trubos-agent,';\r\n        }\r\n        if(ai.name === 'TurboS长文版@元器'){\r\n          this.userInfoReq.roles = this.userInfoReq.roles + 'cube-turbos-large-agent,';\r\n        }\r\n        if(ai.name === 'MiniMax@元器'){\r\n          this.userInfoReq.roles = this.userInfoReq.roles + 'cube-mini-max-agent,';\r\n        }\r\n        // if(ai.name === '搜狗搜索@元器'){\r\n        //   this.userInfoReq.roles = this.userInfoReq.roles + 'cube-sogou-agent,';\r\n        // }\r\n        // if(ai.name === 'KIMI@元器'){\r\n        //   this.userInfoReq.roles = this.userInfoReq.roles + 'cube-lwss-agent,';\r\n        // }\r\n        if(ai.name === '豆包'){\r\n          this.userInfoReq.roles = this.userInfoReq.roles + 'zj-db,';\r\n          if (ai.selectedCapabilities.includes(\"deep_thinking\")) {\r\n            this.userInfoReq.roles = this.userInfoReq.roles + 'zj-db-sdsk,';\r\n          }\r\n        }\r\n        if(ai.name === '百度AI'){\r\n          this.userInfoReq.roles = this.userInfoReq.roles + 'wx-';\r\n          // 添加模型选择\r\n          if (ai.selectedCapabilities.includes(\"deepseek-r1\")) {\r\n            this.userInfoReq.roles = this.userInfoReq.roles + 'deepseek-r1,';\r\n          } else if (ai.selectedCapabilities.includes(\"deepseek-v3\")) {\r\n            this.userInfoReq.roles = this.userInfoReq.roles + 'deepseek-v3,';\r\n          } else {\r\n            this.userInfoReq.roles = this.userInfoReq.roles + 'deepseek-r1,'; // 默认模型\r\n          }\r\n          // 添加联网搜索选项\r\n          if (ai.selectedCapabilities.includes(\"internet-search\")) {\r\n            this.userInfoReq.roles = this.userInfoReq.roles + 'internet-search,';\r\n          }\r\n        }\r\n      });\r\n\r\n      console.log(\"参数：\", this.userInfoReq)\r\n\r\n      //调用后端接口\r\n      this.jsonRpcReqest.method = \"使用F8S\"\r\n      this.jsonRpcReqest.params = this.userInfoReq\r\n      this.message(this.jsonRpcReqest)\r\n      this.userInfoReq.isNewChat = false;\r\n    },\r\n\r\n    message(data) {\r\n      message(data).then(res => {\r\n        if (res.code == 201) {\r\n          uni.showToast({\r\n            title: res.messages,\r\n            icon: 'none',\r\n            duration: 1500,\r\n          });\r\n        }\r\n      })\r\n\r\n    },\r\n    toggleCapability(ai, capabilityValue) {\r\n      if (!ai.enabled) return;\r\n\r\n      const index = ai.selectedCapabilities.indexOf(capabilityValue);\r\n      console.log('切换前:', ai.selectedCapabilities);\r\n\r\n      // 百度AI的模型选择逻辑：DeepSeek-R1和DeepSeek-V3只能选一个\r\n      if (ai.name === '百度AI' && (capabilityValue === 'deepseek-r1' || capabilityValue === 'deepseek-v3')) {\r\n        const newCapabilities = [...ai.selectedCapabilities];\r\n        // 移除所有模型选择\r\n        const modelIndex1 = newCapabilities.indexOf('deepseek-r1');\r\n        const modelIndex2 = newCapabilities.indexOf('deepseek-v3');\r\n        if (modelIndex1 !== -1) newCapabilities.splice(modelIndex1, 1);\r\n        if (modelIndex2 !== -1) newCapabilities.splice(modelIndex2, 1);\r\n\r\n        // 如果当前选择的不是已选中的模型，则添加新选择\r\n        if (index === -1) {\r\n          newCapabilities.push(capabilityValue);\r\n        }\r\n        // 如果点击的是已选中的模型，则不添加（保持取消选择的效果）\r\n\r\n        this.$set(ai, 'selectedCapabilities', newCapabilities);\r\n      } else {\r\n        // 其他AI或联网搜索的正常切换逻辑\r\n        if (index === -1) {\r\n          // 如果不存在，则添加\r\n          this.$set(ai.selectedCapabilities, ai.selectedCapabilities.length, capabilityValue);\r\n        } else {\r\n          // 如果已存在，则移除\r\n          const newCapabilities = [...ai.selectedCapabilities];\r\n          newCapabilities.splice(index, 1);\r\n          this.$set(ai, 'selectedCapabilities', newCapabilities);\r\n        }\r\n      }\r\n\r\n      console.log('切换后:', ai.selectedCapabilities);\r\n      this.$forceUpdate(); // 强制更新视图\r\n    },\r\n    getStatusText(status) {\r\n      switch (status) {\r\n        case 'idle': return '等待中';\r\n        case 'running': return '正在执行';\r\n        case 'completed': return '已完成';\r\n        case 'failed': return '执行失败';\r\n        default: return '未知状态';\r\n      }\r\n    },\r\n    getStatusIcon(status) {\r\n      switch (status) {\r\n        case 'idle': return 'el-icon-time';\r\n        case 'running': return 'el-icon-loading';\r\n        case 'completed': return 'el-icon-check success-icon';\r\n        case 'failed': return 'el-icon-close error-icon';\r\n        default: return 'el-icon-question';\r\n      }\r\n    },\r\n    renderMarkdown(text) {\r\n      return marked(text);\r\n    },\r\n    // HTML转纯文本\r\n    htmlToText(html) {\r\n      const tempDiv = document.createElement('div');\r\n      tempDiv.innerHTML = html;\r\n      return tempDiv.textContent || tempDiv.innerText || '';\r\n    },\r\n\r\n    // HTML转Markdown\r\n    htmlToMarkdown(html) {\r\n      return this.turndownService.turndown(html);\r\n    },\r\n\r\n    copyResult(content) {\r\n      // 将HTML转换为纯文本\r\n      const plainText = this.htmlToText(content);\r\n      const textarea = document.createElement('textarea');\r\n      textarea.value = plainText;\r\n      document.body.appendChild(textarea);\r\n      textarea.select();\r\n      document.execCommand('copy');\r\n      document.body.removeChild(textarea);\r\n      this.$message.success('已复制纯文本到剪贴板');\r\n    },\r\n\r\n    exportResult(result) {\r\n      // 将HTML转换为Markdown\r\n      const markdown = result.content;\r\n      const blob = new Blob([markdown], { type: 'text/markdown' });\r\n      const link = document.createElement('a');\r\n      link.href = URL.createObjectURL(blob);\r\n      link.download = `${result.aiName}_结果_${new Date().toISOString().slice(0, 10)}.md`;\r\n      link.click();\r\n      URL.revokeObjectURL(link.href);\r\n      this.$message.success('已导出Markdown文件');\r\n    },\r\n\r\n    openShareUrl(shareUrl) {\r\n      if (shareUrl) {\r\n        window.open(shareUrl, '_blank');\r\n      } else {\r\n        this.$message.warning('暂无原链接');\r\n      }\r\n    },\r\n    showLargeImage(imageUrl) {\r\n      this.currentLargeImage = imageUrl;\r\n      this.showImageDialog = true;\r\n      // 找到当前图片的索引，设置轮播图的初始位置\r\n      const currentIndex = this.screenshots.indexOf(imageUrl);\r\n      if (currentIndex !== -1) {\r\n        this.$nextTick(() => {\r\n          const carousel = this.$el.querySelector('.image-dialog .el-carousel');\r\n          if (carousel && carousel.__vue__) {\r\n            carousel.__vue__.setActiveItem(currentIndex);\r\n          }\r\n        });\r\n      }\r\n    },\r\n    closeLargeImage() {\r\n      this.showImageDialog = false;\r\n      this.currentLargeImage = '';\r\n    },\r\n    // WebSocket 相关方法\r\n    initWebSocket(id) {\r\n      const wsUrl = process.env.VUE_APP_WS_API + `mypc-${id}`;\r\n      console.log('WebSocket URL:', process.env.VUE_APP_WS_API);\r\n      websocketClient.connect(wsUrl, (event) => {\r\n        switch (event.type) {\r\n          case 'open':\r\n            // this.$message.success('');\r\n            break;\r\n          case 'message':\r\n            this.handleWebSocketMessage(event.data);\r\n            break;\r\n          case 'close':\r\n            this.$message.warning('WebSocket连接已关闭');\r\n            break;\r\n          case 'error':\r\n            this.$message.error('WebSocket连接错误');\r\n            break;\r\n          case 'reconnect_failed':\r\n            this.$message.error('WebSocket重连失败，请刷新页面重试');\r\n            break;\r\n        }\r\n      });\r\n    },\r\n\r\n    handleWebSocketMessage(data) {\r\n\r\n      const datastr = data;\r\n      const dataObj = JSON.parse(datastr);\r\n\r\n      // 处理chatId消息\r\n      if (dataObj.type === 'RETURN_YBT1_CHATID' && dataObj.chatId) {\r\n        this.userInfoReq.toneChatId = dataObj.chatId;\r\n      } else if (dataObj.type === 'RETURN_YBDS_CHATID' && dataObj.chatId) {\r\n        this.userInfoReq.ybDsChatId = dataObj.chatId;\r\n      } else if (dataObj.type === 'RETURN_DB_CHATID' && dataObj.chatId) {\r\n        this.userInfoReq.dbChatId = dataObj.chatId;\r\n      }\r\n\r\n      // 处理进度日志消息\r\n      if (dataObj.type === 'RETURN_PC_TASK_LOG' && dataObj.aiName) {\r\n        const targetAI = this.enabledAIs.find(ai => ai.name === dataObj.aiName);\r\n        if (targetAI) {\r\n          // 将新进度添加到数组开头\r\n          targetAI.progressLogs.unshift({\r\n            content: dataObj.content,\r\n            timestamp: new Date(),\r\n            isCompleted: false\r\n          });\r\n        }\r\n        return;\r\n      }\r\n\r\n      // 处理截图消息\r\n      if (dataObj.type === 'RETURN_PC_TASK_IMG' && dataObj.url) {\r\n        // 将新的截图添加到数组开头\r\n        this.screenshots.unshift(dataObj.url);\r\n        return;\r\n      }\r\n\r\n              // 处理智能评分结果\r\n      if (dataObj.type === 'RETURN_WKPF_RES') {\r\n        const wkpfAI = this.enabledAIs.find(ai => ai.name === '智能评分');\r\n        if (wkpfAI) {\r\n          this.$set(wkpfAI, 'status', 'completed');\r\n          if (wkpfAI.progressLogs.length > 0) {\r\n            this.$set(wkpfAI.progressLogs[0], 'isCompleted', true);\r\n          }\r\n          // 添加评分结果到results最前面\r\n          this.results.unshift({\r\n            aiName: '智能评分',\r\n            content: dataObj.draftContent,\r\n            shareUrl: dataObj.shareUrl || '',\r\n            shareImgUrl: dataObj.shareImgUrl || '',\r\n            timestamp: new Date()\r\n          });\r\n          this.activeResultTab = 'result-0';\r\n\r\n          // 智能评分完成时，再次保存历史记录\r\n          this.saveHistory();\r\n        }\r\n        return;\r\n      }\r\n\r\n      // 根据消息类型更新对应AI的状态和结果\r\n      let targetAI = null;\r\n      switch (dataObj.type) {\r\n        case 'RETURN_YBT1_RES':\r\n          console.log('收到消息:', data);\r\n          targetAI = this.enabledAIs.find(ai => ai.name === '腾讯元宝T1');\r\n          break;\r\n        case 'RETURN_YBDS_RES':\r\n          console.log('收到消息:', data);\r\n          targetAI = this.enabledAIs.find(ai => ai.name === '腾讯元宝DS');\r\n          break;\r\n        case 'RETURN_DB_RES':\r\n          console.log('收到消息:', data);\r\n          targetAI = this.enabledAIs.find(ai => ai.name === '豆包');\r\n          break;\r\n        case 'RETURN_WX_RES':\r\n          console.log('收到消息:', data);\r\n          targetAI = this.enabledAIs.find(ai => ai.name === '百度AI');\r\n          break;\r\n        case 'RETURN_TURBOS_RES':\r\n          console.log('收到消息:', data);\r\n          targetAI = this.enabledAIs.find(ai => ai.name === 'TurboS@元器');\r\n          break;\r\n        case 'RETURN_TURBOS_LARGE_RES':\r\n          console.log('收到消息:', data);\r\n          targetAI = this.enabledAIs.find(ai => ai.name === 'TurboS长文版@元器');\r\n          break;\r\n        // case 'RETURN_MINI_MAX_RES':\r\n        //   targetAI = this.enabledAIs.find(ai => ai.name === 'MiniMax@元器');\r\n        //   break;\r\n      }\r\n\r\n      if (targetAI) {\r\n        // 更新AI状态为已完成\r\n        this.$set(targetAI, 'status', 'completed');\r\n\r\n        // 将最后一条进度消息标记为已完成\r\n        if (targetAI.progressLogs.length > 0) {\r\n          this.$set(targetAI.progressLogs[0], 'isCompleted', true);\r\n        }\r\n\r\n        // 添加结果到数组开头\r\n        const resultIndex = this.results.findIndex(r => r.aiName === targetAI.name);\r\n        if (resultIndex === -1) {\r\n          this.results.unshift({\r\n            aiName: targetAI.name,\r\n            content: dataObj.draftContent,\r\n            shareUrl: dataObj.shareUrl || '',\r\n            shareImgUrl: dataObj.shareImgUrl || '',\r\n            timestamp: new Date()\r\n          });\r\n          this.activeResultTab = 'result-0';\r\n        } else {\r\n          this.results.splice(resultIndex, 1);\r\n          this.results.unshift({\r\n            aiName: targetAI.name,\r\n            content: dataObj.draftContent,\r\n            shareUrl: dataObj.shareUrl || '',\r\n            shareImgUrl: dataObj.shareImgUrl || '',\r\n            timestamp: new Date()\r\n          });\r\n          this.activeResultTab = 'result-0';\r\n        }\r\n        this.saveHistory();\r\n      }\r\n\r\n      // 检查是否所有任务都已完成\r\n      // const allCompleted = this.enabledAIs.every(ai =>\r\n      //   ai.status === 'completed' || ai.status === 'failed'\r\n      // );\r\n\r\n      // if (allCompleted) {\r\n      //\r\n      // }\r\n    },\r\n\r\n    closeWebSocket() {\r\n      websocketClient.close();\r\n    },\r\n\r\n    sendMessage(data) {\r\n      if (websocketClient.send(data)) {\r\n        // 滚动到底部\r\n        this.$nextTick(() => {\r\n          this.scrollToBottom();\r\n        });\r\n      } else {\r\n        this.$message.error('WebSocket未连接');\r\n      }\r\n    },\r\n    toggleAIExpansion(ai) {\r\n      this.$set(ai, 'isExpanded', !ai.isExpanded);\r\n    },\r\n\r\n    formatTime(timestamp) {\r\n      const date = new Date(timestamp);\r\n      return date.toLocaleTimeString('zh-CN', {\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n        second: '2-digit',\r\n        hour12: false\r\n      });\r\n    },\r\n    showScoreDialog() {\r\n      this.scoreDialogVisible = true;\r\n      this.selectedResults = [];\r\n    },\r\n\r\n    handleScore() {\r\n      if (!this.canScore) return;\r\n\r\n      // 获取选中的结果内容并按照指定格式拼接\r\n      const selectedContents = this.results\r\n        .filter(result => this.selectedResults.includes(result.aiName))\r\n        .map(result => {\r\n          // 将HTML内容转换为纯文本\r\n          const plainContent = this.htmlToText(result.content);\r\n          return `${result.aiName}初稿：\\n${plainContent}\\n`;\r\n        })\r\n        .join('\\n');\r\n\r\n      // 构建完整的评分提示内容\r\n      const fullPrompt = `${this.scorePrompt}\\n${selectedContents}`;\r\n\r\n      // 构建评分请求\r\n      const scoreRequest = {\r\n        jsonrpc: '2.0',\r\n        id: uuidv4(),\r\n        method: 'AI评分',\r\n        params: {\r\n          taskId: uuidv4(),\r\n          userId: this.userId,\r\n          corpId: this.corpId,\r\n          userPrompt: fullPrompt,\r\n          roles: 'zj-db-sdsk' // 默认使用豆包进行评分\r\n        }\r\n      };\r\n\r\n      // 发送评分请求\r\n      console.log(\"参数\", scoreRequest)\r\n      this.message(scoreRequest);\r\n      this.scoreDialogVisible = false;\r\n\r\n      // 创建智能评分AI节点\r\n      const wkpfAI = {\r\n        name: '智能评分',\r\n        avatar: require('../../../assets/ai/yuanbao.png'),\r\n        capabilities: [\r\n          { label: 'DeepSeek-R1最新版', value: 'deepseek-r1' },\r\n          { label: 'DeepSeek-V3最新版', value: 'deepseek-v3' }\r\n        ],\r\n        selectedCapabilities: ['deepseek-r1'],\r\n        enabled: true,\r\n        status: 'running',\r\n        progressLogs: [\r\n          {\r\n            content: '智能评分任务已提交，正在评分...',\r\n            timestamp: new Date(),\r\n            isCompleted: false,\r\n            type: '智能评分'\r\n          }\r\n        ],\r\n        isExpanded: true\r\n      };\r\n\r\n      // 检查是否已存在智能评分\r\n      const existIndex = this.enabledAIs.findIndex(ai => ai.name === '智能评分');\r\n      if (existIndex === -1) {\r\n        // 如果不存在，添加到数组开头\r\n        this.enabledAIs.unshift(wkpfAI);\r\n      } else {\r\n        // 如果已存在，更新状态和日志\r\n        this.enabledAIs[existIndex] = wkpfAI;\r\n        // 将智能评分移到数组开头\r\n        const wkpf = this.enabledAIs.splice(existIndex, 1)[0];\r\n        this.enabledAIs.unshift(wkpf);\r\n      }\r\n\r\n      this.$forceUpdate();\r\n      this.$message.success('评分请求已发送，请等待结果');\r\n    },\r\n    // 显示历史记录抽屉\r\n    showHistoryDrawer() {\r\n      this.historyDrawerVisible = true;\r\n      this.loadChatHistory(1);\r\n    },\r\n\r\n    // 关闭历史记录抽屉\r\n    handleHistoryDrawerClose() {\r\n      this.historyDrawerVisible = false;\r\n    },\r\n\r\n    // 加载历史记录\r\n    async loadChatHistory(isAll) {\r\n      try {\r\n        const res = await getChatHistory(this.userId, isAll);\r\n        if (res.code === 200) {\r\n          this.chatHistory = res.data || [];\r\n        }\r\n      } catch (error) {\r\n        console.error('加载历史记录失败:', error);\r\n        this.$message.error('加载历史记录失败');\r\n      }\r\n    },\r\n\r\n    // 格式化历史记录时间\r\n    formatHistoryTime(timestamp) {\r\n      const date = new Date(timestamp);\r\n      return date.toLocaleTimeString('zh-CN', {\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n        hour12: false\r\n      });\r\n    },\r\n\r\n    // 获取历史记录日期分组\r\n    getHistoryDate(timestamp) {\r\n      const date = new Date(timestamp);\r\n      const today = new Date();\r\n      const yesterday = new Date(today);\r\n      yesterday.setDate(yesterday.getDate() - 1);\r\n      const twoDaysAgo = new Date(today);\r\n      twoDaysAgo.setDate(twoDaysAgo.getDate() - 2);\r\n      const threeDaysAgo = new Date(today);\r\n      threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);\r\n\r\n      if (date.toDateString() === today.toDateString()) {\r\n        return '今天';\r\n      } else if (date.toDateString() === yesterday.toDateString()) {\r\n        return '昨天';\r\n      } else if (date.toDateString() === twoDaysAgo.toDateString()) {\r\n        return '两天前';\r\n      } else if (date.toDateString() === threeDaysAgo.toDateString()) {\r\n        return '三天前';\r\n      } else {\r\n        return date.toLocaleDateString('zh-CN', {\r\n          year: 'numeric',\r\n          month: 'long',\r\n          day: 'numeric'\r\n        });\r\n      }\r\n    },\r\n\r\n    // 加载历史记录项\r\n    loadHistoryItem(item) {\r\n      try {\r\n        const historyData = JSON.parse(item.data);\r\n        // 恢复AI选择配置\r\n        this.aiList = historyData.aiList || this.aiList;\r\n        // 恢复提示词输入\r\n        this.promptInput = historyData.promptInput || '';\r\n        // 恢复任务流程\r\n        this.enabledAIs = historyData.enabledAIs || [];\r\n        // 恢复主机可视化\r\n        this.screenshots = historyData.screenshots || [];\r\n        // 恢复执行结果\r\n        this.results = historyData.results || [];\r\n        // 恢复chatId\r\n        this.chatId = item.chatId || this.chatId;\r\n        this.userInfoReq.toneChatId = item.toneChatId || '';\r\n        this.userInfoReq.ybDsChatId = item.ybDsChatId || '';\r\n        this.userInfoReq.dbChatId = item.dbChatId || '';\r\n        this.userInfoReq.isNewChat = false;\r\n\r\n        // 展开相关区域\r\n        this.activeCollapses = ['ai-selection', 'prompt-input'];\r\n        this.taskStarted = true;\r\n\r\n        this.$message.success('历史记录加载成功');\r\n        this.historyDrawerVisible = false;\r\n      } catch (error) {\r\n        console.error('加载历史记录失败:', error);\r\n        this.$message.error('加载历史记录失败');\r\n      }\r\n    },\r\n\r\n    // 保存历史记录\r\n    async saveHistory() {\r\n      // if (!this.taskStarted || this.enabledAIs.some(ai => ai.status === 'running')) {\r\n      //   return;\r\n      // }\r\n\r\n      const historyData = {\r\n        aiList: this.aiList,\r\n        promptInput: this.promptInput,\r\n        enabledAIs: this.enabledAIs,\r\n        screenshots: this.screenshots,\r\n        results: this.results,\r\n        chatId: this.chatId,\r\n        toneChatId: this.userInfoReq.toneChatId,\r\n        ybDsChatId: this.userInfoReq.ybDsChatId,\r\n        dbChatId: this.userInfoReq.dbChatId\r\n      };\r\n\r\n      try {\r\n        await saveUserChatData({\r\n          userId: this.userId,\r\n          userPrompt: this.promptInput,\r\n          data: JSON.stringify(historyData),\r\n          chatId: this.chatId,\r\n          toneChatId: this.userInfoReq.toneChatId,\r\n          ybDsChatId: this.userInfoReq.ybDsChatId,\r\n          dbChatId: this.userInfoReq.dbChatId\r\n        });\r\n      } catch (error) {\r\n        console.error('保存历史记录失败:', error);\r\n        this.$message.error('保存历史记录失败');\r\n      }\r\n    },\r\n\r\n    // 修改折叠切换方法\r\n    toggleHistoryExpansion(item) {\r\n      this.$set(this.expandedHistoryItems, item.chatId, !this.expandedHistoryItems[item.chatId]);\r\n    },\r\n\r\n    // 创建新对话\r\n    createNewChat() {\r\n      // 重置所有数据\r\n      this.chatId = uuidv4();\r\n      this.isNewChat = true;\r\n      this.promptInput = '';\r\n      this.taskStarted = false;\r\n      this.screenshots = [];\r\n      this.results = [];\r\n      this.enabledAIs = [];\r\n      this.userInfoReq = {\r\n        userPrompt: '',\r\n        userId: this.userId,\r\n        corpId: this.corpId,\r\n        taskId: '',\r\n        roles: '',\r\n        toneChatId: '',\r\n        ybDsChatId: '',\r\n        dbChatId: '',\r\n        isNewChat: true\r\n      };\r\n      // 重置AI列表为初始状态\r\n      this.aiList = [\r\n        {\r\n          name: 'TurboS@元器',\r\n          avatar: require('../../../assets/ai/yuanbao.png'),\r\n          capabilities: [],\r\n          selectedCapabilities: [],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        },\r\n        {\r\n          name: 'TurboS长文版@元器',\r\n          avatar: require('../../../assets/ai/yuanbao.png'),\r\n          capabilities: [],\r\n          selectedCapabilities: [],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        },\r\n        // {\r\n        //   name: 'MiniMax@元器',\r\n        //   avatar: require('../../../assets/ai/yuanbao.png'),\r\n        //   capabilities: [],\r\n        //   selectedCapabilities: [],\r\n        //   enabled: true,\r\n        //   status: 'idle',\r\n        //   progressLogs: [],\r\n        //   isExpanded: true\r\n        // },\r\n        // {\r\n        //   name: 'KIMI@元器',\r\n        //   avatar: require('../../../assets/ai/yuanbao.png'),\r\n        //   capabilities: [],\r\n        //   selectedCapabilities: [],\r\n        //   enabled: true,\r\n        //   status: 'idle',\r\n        //   progressLogs: [],\r\n        //   isExpanded: true\r\n        // },\r\n        {\r\n          name: '腾讯元宝T1',\r\n          avatar: require('../../../assets/ai/yuanbao.png'),\r\n          capabilities: [\r\n            { label: '深度思考', value: 'deep_thinking' },\r\n            { label: '联网搜索', value: 'web_search' }\r\n          ],\r\n          selectedCapabilities: ['deep_thinking','web_search'],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        },\r\n        {\r\n          name: '腾讯元宝DS',\r\n          avatar: require('../../../assets/ai/yuanbao.png'),\r\n          capabilities: [\r\n            { label: '深度思考', value: 'deep_thinking' },\r\n            { label: '联网搜索', value: 'web_search' }\r\n          ],\r\n          selectedCapabilities: ['deep_thinking','web_search'],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        },\r\n        {\r\n          name: '豆包',\r\n          avatar: require('../../../assets/ai/豆包.png'),\r\n          capabilities: [\r\n            { label: '深度思考', value: 'deep_thinking' }\r\n          ],\r\n          selectedCapabilities: ['deep_thinking'],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        },\r\n        {\r\n          name: '百度AI',\r\n          avatar: require('../../../assets/ai/baidu.png'),\r\n          capabilities: [\r\n            { label: 'DeepSeek-R1最新版', value: 'deepseek-r1' },\r\n            { label: 'DeepSeek-V3最新版', value: 'deepseek-v3' },\r\n            { label: '联网搜索', value: 'internet-search' }\r\n          ],\r\n          selectedCapabilities: ['deepseek-r1'],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        }\r\n      ];\r\n      // 展开相关区域\r\n      this.activeCollapses = ['ai-selection', 'prompt-input'];\r\n\r\n      this.$message.success('已创建新对话');\r\n    },\r\n\r\n    // 加载上次会话\r\n    async loadLastChat() {\r\n      try {\r\n        const res = await getChatHistory(this.userId,0);\r\n        if (res.code === 200 && res.data && res.data.length > 0) {\r\n          // 获取最新的会话记录\r\n          const lastChat = res.data[0];\r\n          this.loadHistoryItem(lastChat);\r\n        }\r\n      } catch (error) {\r\n        console.error('加载上次会话失败:', error);\r\n      }\r\n    },\r\n\r\n    // 判断是否为图片文件\r\n    isImageFile(url) {\r\n      if (!url) return false;\r\n      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'];\r\n      const urlLower = url.toLowerCase();\r\n      return imageExtensions.some(ext => urlLower.includes(ext));\r\n    },\r\n\r\n    // 判断是否为PDF文件\r\n    isPdfFile(url) {\r\n      if (!url) return false;\r\n      return url.toLowerCase().includes('.pdf');\r\n    },\r\n\r\n    // 根据AI名称获取图片样式\r\n    getImageStyle(aiName) {\r\n      const widthMap = {\r\n        'TurboS@元器': '700px',\r\n        '腾讯元宝DS': '700px',\r\n        'TurboS长文版@元器': '700px',\r\n        '腾讯元宝T1': '700px',\r\n        '豆包': '560px',\r\n        '百度AI': '700px'\r\n      };\r\n\r\n      const width = widthMap[aiName] || '560px'; // 默认宽度\r\n\r\n      return {\r\n        width: width,\r\n        height: 'auto'\r\n      };\r\n    },\r\n\r\n    // 投递到公众号\r\n    handlePushToWechat(result) {\r\n      if (this.pushingToWechat) return; // 防止重复点击\r\n\r\n      this.pushingToWechat = true; // 开始loading\r\n      this.pushOfficeNum += 1; // 递增编号\r\n\r\n      const params = {\r\n        contentText: result.content,\r\n        shareUrl: result.shareUrl,\r\n        userId: this.userId,\r\n        num: this.pushOfficeNum,\r\n        aiName: result.aiName\r\n      };\r\n\r\n      pushAutoOffice(params).then(res => {\r\n        if (res.code === 200) {\r\n          this.$message.success('投递到公众号成功！');\r\n        } else {\r\n          this.$message.error(res.msg || '投递失败，请重试');\r\n        }\r\n      }).catch(error => {\r\n        console.error('投递到公众号失败:', error);\r\n        this.$message.error('投递失败，请重试');\r\n      }).finally(() => {\r\n        this.pushingToWechat = false; // 结束loading\r\n      });\r\n    },\r\n\r\n\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.ai-management-platform {\r\n  min-height: 100vh;\r\n  background-color: #f5f7fa;\r\n  padding-bottom: 30px;\r\n}\r\n\r\n.top-nav {\r\n  background-color: #fff;\r\n  padding: 15px 20px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n  margin-bottom: 20px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.logo-area {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.logo {\r\n  height: 36px;\r\n  margin-right: 10px;\r\n}\r\n\r\n.platform-title {\r\n  margin: 0;\r\n  font-size: 20px;\r\n  color: #303133;\r\n}\r\n\r\n.main-content {\r\n  padding: 0 30px;\r\n  width: 90%;\r\n  margin: 0 auto;\r\n}\r\n::v-deep .el-collapse-item__header {\r\n  font-size: 16px;\r\n  color: #333;\r\n  padding-left: 20px;\r\n}\r\n.section-title {\r\n  font-size: 18px;\r\n  color: #606266;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.ai-cards {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 20px;\r\n  margin-bottom: 0px;\r\n  margin-left: 20px;\r\n  margin-top: 10px;\r\n}\r\n\r\n.ai-card {\r\n  width: calc(25% - 20px);\r\n  box-sizing: border-box;\r\n}\r\n\r\n.ai-card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.ai-left {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.ai-avatar {\r\n  margin-right: 10px;\r\n}\r\n\r\n.ai-avatar img {\r\n  width: 30px;\r\n  height: 30px;\r\n  border-radius: 50%;\r\n  object-fit: cover;\r\n}\r\n\r\n.ai-name {\r\n  font-weight: bold;\r\n  font-size: 12px;\r\n}\r\n\r\n.ai-status {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.ai-capabilities {\r\n  margin: 15px 0;\r\n  width: 100%;\r\n  display: flex;\r\n  justify-content: center;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.button-capability-group {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  justify-content: center;\r\n  gap: 8px;\r\n}\r\n\r\n.button-capability-group .el-button {\r\n  margin: 0;\r\n  border-radius: 16px;\r\n  padding: 6px 12px;\r\n}\r\n\r\n.button-capability-group .el-button.is-plain:hover,\r\n.button-capability-group .el-button.is-plain:focus {\r\n  background: #ecf5ff;\r\n  border-color: #b3d8ff;\r\n  color: #409EFF;\r\n}\r\n\r\n.prompt-input-section {\r\n  margin-bottom: 30px;\r\n  padding: 0 20px 0 0px;\r\n}\r\n\r\n.prompt-input {\r\n  margin-bottom: 10px;\r\n  margin-left: 20px;\r\n  width: 99%;\r\n}\r\n\r\n.prompt-footer {\r\n  display: flex;\r\n  margin-bottom: -30px;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.word-count {\r\n  font-size: 12px;\r\n  padding-left: 20px;\r\n}\r\n\r\n.send-button {\r\n  padding: 10px 20px;\r\n}\r\n\r\n.execution-status-section {\r\n  margin-bottom: 30px;\r\n  padding:20px 0px 0px 0px;\r\n}\r\n\r\n.task-flow-card, .screenshots-card {\r\n  height: 800px;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.task-flow {\r\n  padding: 15px;\r\n  height: 800px;\r\n  overflow-y: auto;\r\n  background-color: #f5f7fa;\r\n  border-radius: 4px;\r\n}\r\n\r\n.task-flow::-webkit-scrollbar {\r\n  width: 6px;\r\n}\r\n\r\n.task-flow::-webkit-scrollbar-thumb {\r\n  background-color: #c0c4cc;\r\n  border-radius: 3px;\r\n}\r\n\r\n.task-flow::-webkit-scrollbar-track {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.task-item {\r\n  margin-bottom: 15px;\r\n  border-radius: 4px;\r\n  background-color: #fff;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n}\r\n\r\n.task-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 12px 15px;\r\n  cursor: pointer;\r\n  transition: background-color 0.3s;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.task-header:hover {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.header-left {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.header-left .el-icon-arrow-right {\r\n  transition: transform 0.3s;\r\n  font-size: 14px;\r\n  color: #909399;\r\n}\r\n\r\n.header-left .el-icon-arrow-right.is-expanded {\r\n  transform: rotate(90deg);\r\n}\r\n\r\n.progress-timeline {\r\n  position: relative;\r\n  margin: 0;\r\n  padding: 15px 0;\r\n}\r\n\r\n.timeline-scroll {\r\n  max-height: 200px;\r\n  overflow-y: auto;\r\n  padding: 0 15px;\r\n}\r\n\r\n.timeline-scroll::-webkit-scrollbar {\r\n  width: 4px;\r\n}\r\n\r\n.timeline-scroll::-webkit-scrollbar-thumb {\r\n  background-color: #c0c4cc;\r\n  border-radius: 2px;\r\n}\r\n\r\n.timeline-scroll::-webkit-scrollbar-track {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.progress-item {\r\n  position: relative;\r\n  padding: 8px 0 8px 20px;\r\n  display: flex;\r\n  align-items: flex-start;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.progress-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.progress-dot {\r\n  position: absolute;\r\n  left: 0;\r\n  top: 12px;\r\n  width: 10px;\r\n  height: 10px;\r\n  border-radius: 50%;\r\n  background-color: #e0e0e0;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.progress-line {\r\n  position: absolute;\r\n  left: 4px;\r\n  top: 22px;\r\n  bottom: -8px;\r\n  width: 2px;\r\n  background-color: #e0e0e0;\r\n}\r\n\r\n.progress-content {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n\r\n.progress-time {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.progress-text {\r\n  font-size: 13px;\r\n  color: #606266;\r\n  line-height: 1.4;\r\n  word-break: break-all;\r\n}\r\n\r\n.progress-item.completed .progress-dot {\r\n  background-color: #67c23a;\r\n}\r\n\r\n.progress-item.completed .progress-line {\r\n  background-color: #67c23a;\r\n}\r\n\r\n.progress-item.current .progress-dot {\r\n  background-color: #409eff;\r\n  animation: pulse 1.5s infinite;\r\n}\r\n\r\n.progress-item.current .progress-line {\r\n  background-color: #409eff;\r\n}\r\n\r\n.ai-name {\r\n  font-weight: 600;\r\n  font-size: 14px;\r\n  color: #303133;\r\n}\r\n\r\n.header-right {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.status-text {\r\n  font-size: 13px;\r\n  color: #606266;\r\n}\r\n\r\n.status-icon {\r\n  font-size: 16px;\r\n}\r\n\r\n.success-icon {\r\n  color: #67c23a;\r\n}\r\n\r\n.error-icon {\r\n  color: #f56c6c;\r\n}\r\n\r\n@keyframes pulse {\r\n  0% {\r\n    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0.4);\r\n  }\r\n  70% {\r\n    box-shadow: 0 0 0 6px rgba(64, 158, 255, 0);\r\n  }\r\n  100% {\r\n    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0);\r\n  }\r\n}\r\n\r\n.screenshot-image {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: contain;\r\n  cursor: pointer;\r\n  transition: transform 0.3s;\r\n}\r\n\r\n.screenshot-image:hover {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.results-section {\r\n  margin-top: 20px;\r\n  padding: 0 10px;\r\n}\r\n\r\n.result-content {\r\n  padding: 20px 30px;\r\n}\r\n\r\n.result-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.result-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n}\r\n\r\n.result-buttons {\r\n  display: flex;\r\n  gap: 10px;\r\n  align-items: center;\r\n}\r\n\r\n.share-link-btn, .push-wechat-btn {\r\n  border-radius: 16px;\r\n  padding: 6px 12px;\r\n}\r\n\r\n.markdown-content {\r\n  margin-bottom: 20px;\r\n  max-height: 400px;\r\n  overflow-y: auto;\r\n  padding: 15px 20px;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n  background-color: #fff;\r\n}\r\n\r\n.action-buttons {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  gap: 10px;\r\n  padding: 0 10px;\r\n}\r\n\r\n@media (max-width: 1200px) {\r\n  .ai-card {\r\n    width: calc(33.33% - 14px);\r\n  }\r\n}\r\n\r\n@media (max-width: 992px) {\r\n  .ai-card {\r\n    width: calc(50% - 10px);\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .ai-card {\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n.el-collapse {\r\n  border-top: none;\r\n  border-bottom: none;\r\n}\r\n\r\n\r\n\r\n.el-collapse-item__content {\r\n  padding: 15px 0;\r\n}\r\n\r\n.ai-selection-section {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.prompt-input-section {\r\n  margin-bottom: 30px;\r\n  padding: 0 20px 0 0px;\r\n}\r\n\r\n.image-dialog .el-dialog__body {\r\n  padding: 0;\r\n}\r\n\r\n.large-image-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  background-color: #000;\r\n}\r\n\r\n.large-image {\r\n  max-width: 100%;\r\n  max-height: 80vh;\r\n  object-fit: contain;\r\n}\r\n\r\n.image-dialog .el-carousel {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.image-dialog .el-carousel__container {\r\n  height: 80vh;\r\n}\r\n\r\n.image-dialog .el-carousel__item {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  background-color: #000;\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.score-dialog-content {\r\n  padding: 20px;\r\n}\r\n\r\n.selected-results {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.result-checkbox {\r\n  margin-right: 20px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.score-prompt-section {\r\n  margin-top: 20px;\r\n}\r\n\r\n.score-prompt-input {\r\n  margin-top: 10px;\r\n}\r\n\r\n.score-prompt-input .el-textarea__inner {\r\n  min-height: 500px !important;\r\n}\r\n\r\n.dialog-footer {\r\n  text-align: right;\r\n}\r\n\r\n.score-dialog .el-dialog {\r\n  height: 95vh;\r\n  margin-top: 2.5vh !important;\r\n}\r\n\r\n.score-dialog .el-dialog__body {\r\n  height: calc(95vh - 120px);\r\n  overflow-y: auto;\r\n  padding: 20px;\r\n}\r\n\r\n.nav-buttons {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 20px;\r\n}\r\n\r\n.history-button {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.history-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n  vertical-align: middle;\r\n}\r\n\r\n.history-content {\r\n  padding: 20px;\r\n}\r\n\r\n.history-group {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.history-date {\r\n  font-size: 14px;\r\n  color: #909399;\r\n  margin-bottom: 10px;\r\n  padding: 5px 0;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.history-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 10px;\r\n}\r\n\r\n.history-item {\r\n  margin-bottom: 15px;\r\n  border-radius: 4px;\r\n  background-color: #f5f7fa;\r\n  overflow: hidden;\r\n}\r\n\r\n.history-parent {\r\n  padding: 10px;\r\n  cursor: pointer;\r\n  transition: background-color 0.3s;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.history-parent:hover {\r\n  background-color: #ecf5ff;\r\n}\r\n\r\n.history-children {\r\n  padding-left: 20px;\r\n  background-color: #fff;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.history-child-item {\r\n  padding: 8px 10px;\r\n  cursor: pointer;\r\n  transition: background-color 0.3s;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.history-child-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.history-child-item:hover {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.history-header {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  gap: 8px;\r\n}\r\n\r\n.history-header .el-icon-arrow-right {\r\n  font-size: 14px;\r\n  color: #909399;\r\n  transition: transform 0.3s;\r\n  cursor: pointer;\r\n  margin-top: 3px;\r\n}\r\n\r\n.history-header .el-icon-arrow-right.is-expanded {\r\n  transform: rotate(90deg);\r\n}\r\n\r\n.history-prompt {\r\n  font-size: 14px;\r\n  color: #303133;\r\n  margin-bottom: 5px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n  flex: 1;\r\n}\r\n\r\n.history-time {\r\n  font-size: 12px;\r\n  color: #909399;\r\n}\r\n\r\n.capability-button {\r\n  transition: all 0.3s;\r\n}\r\n\r\n.capability-button.el-button--primary {\r\n  background-color: #409EFF;\r\n  border-color: #409EFF;\r\n  color: #fff;\r\n}\r\n\r\n.capability-button.el-button--info {\r\n  background-color: #fff;\r\n  border-color: #dcdfe6;\r\n  color: #606266;\r\n}\r\n\r\n.capability-button.el-button--info:hover {\r\n  color: #409EFF;\r\n  border-color: #c6e2ff;\r\n  background-color: #ecf5ff;\r\n}\r\n\r\n.capability-button.el-button--primary:hover {\r\n  background-color: #66b1ff;\r\n  border-color: #66b1ff;\r\n  color: #fff;\r\n}\r\n\r\n/* 分享内容样式 */\r\n.share-content {\r\n  margin-bottom: 20px;\r\n  padding: 15px 20px;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n  background-color: #fff;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: flex-start;\r\n  min-height: 600px;\r\n  max-height: 800px;\r\n  overflow: auto;\r\n}\r\n\r\n.share-image {\r\n  object-fit: contain;\r\n  display: block;\r\n}\r\n\r\n.share-pdf {\r\n  width: 100%;\r\n  height: 600px;\r\n  border: none;\r\n  border-radius: 4px;\r\n}\r\n\r\n.share-file {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 200px;\r\n  flex-direction: column;\r\n  color: #909399;\r\n}\r\n\r\n.single-image-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  width: 100%;\r\n  height: 80vh;\r\n}\r\n\r\n.single-image-container .large-image {\r\n  max-width: 100%;\r\n  max-height: 100%;\r\n  object-fit: contain;\r\n}\r\n</style>\r\n"]}]}