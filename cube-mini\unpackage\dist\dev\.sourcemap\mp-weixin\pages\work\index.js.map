{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:////Users/<USER>/AGI/u3w/U3W-AI/cube-mini/pages/work/index.vue?abe7", "webpack:////Users/<USER>/AGI/u3w/U3W-AI/cube-mini/pages/work/index.vue?97d7", "webpack:////Users/<USER>/AGI/u3w/U3W-AI/cube-mini/pages/work/index.vue?cefc", "uni-app:///pages/work/index.vue", "webpack:////Users/<USER>/AGI/u3w/U3W-AI/cube-mini/pages/work/index.vue?b9fa", "webpack:////Users/<USER>/AGI/u3w/U3W-AI/cube-mini/pages/work/index.vue?6785"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "userId", "corpId", "chatId", "expandedHistoryItems", "userInfoReq", "userPrompt", "taskId", "roles", "toneChatId", "ybDsChatId", "dbChatId", "isNewChat", "jsonRpcReqest", "jsonrpc", "id", "method", "params", "sectionExpanded", "aiConfig", "promptInput", "taskStatus", "aiList", "avatar", "capabilities", "selectedCapabilities", "enabled", "status", "progressLogs", "isExpanded", "label", "value", "taskStarted", "enabledAIs", "screenshots", "autoPlay", "results", "activeResultIndex", "chatHistory", "selectedResults", "scorePrompt", "collectNum", "layoutPrompt", "socketTask", "reconnectTimer", "heartbeatTimer", "reconnectCount", "maxReconnectCount", "isConnecting", "scrollIntoView", "historyDrawerVisible", "scoreModalVisible", "layoutModalVisible", "currentLayoutResult", "aiLoginStatus", "yuanbao", "do<PERSON>o", "agent", "deepseek", "accounts", "isLoading", "computed", "canSend", "canScore", "console", "currentResult", "groupedHistory", "chatGroups", "Object", "chatGroup", "groups", "parentItem", "isParent", "children", "child", "onLoad", "uni", "title", "content", "showCancel", "confirmText", "success", "url", "onUnload", "methods", "initUserInfo", "generateUUID", "toggleSection", "toggleAI", "ai", "toggleCapability", "sendPrompt", "icon", "initWebSocket", "fail", "duration", "handleReconnect", "startHeartbeat", "type", "timestamp", "stopHeartbeat", "clearInterval", "sendWebSocketMessage", "message", "closeWebSocket", "clearTimeout", "handleWebSocketMessage", "targetAI", "isCompleted", "wkpfAI", "aiName", "shareUrl", "shareImgUrl", "znpbAI", "handleAiStatusMessage", "handleAIResult", "getStatusText", "getStatusIconClass", "getStatusEmoji", "toggleTaskExpansion", "toggleAutoPlay", "previewImage", "current", "urls", "switchResultTab", "renderMarkdown", "isImageFile", "isPdfFile", "copyResult", "exportResult", "openShareUrl", "copyPdfUrl", "openPdfFile", "filePath", "showHistoryDrawer", "closeHistoryDrawer", "loadChatHistory", "res", "loadHistoryItem", "loadLastChat", "lastChat", "saveHistory", "historyData", "getHistoryDate", "date", "year", "month", "day", "hour", "minute", "second", "parseInt", "yesterday", "formatHistoryTime", "toggleHistoryExpansion", "showScoreModal", "closeScoreModal", "showLayoutModal", "closeLayoutModal", "handleLayout", "handlePushToWechat", "contentText", "num", "toggleResultSelection", "handleScore", "filter", "map", "join", "createNewChat", "checkAiLoginStatus", "setTimeout", "sendAiStatusCheck", "getPlatformIcon", "getPlatformName", "refreshAiStatus", "isAiLoginEnabled", "isAiInLoading", "disableAIsByLoginStatus", "updateAiEnabledStatus", "formatTime"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACqC;;;AAGzF;AACmN;AACnN,gBAAgB,iNAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/IA;AAAA;AAAA;AAAA;AAAk1B,CAAgB,myBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;ACgTt2B;AAGA;AAGA;AAGA;AACA;AAAA;AAAA;AAAA,eAGA;EACAC;EACAC;IACA;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAL;QACAC;QACAK;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;MAEA;MACAC;QACAC;QACAC;QACAC;MACA;MAEA;MACAC;QACAvB;QACAwB;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACA9B;QACAwB;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACA9B;QACAwB;QACAC;UACAM;UACAC;QACA,GACA;UACAD;UACAC;QACA,EACA;QACAN;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACA9B;QACAwB;QACAC;UACAM;UACAC;QACA,GACA;UACAD;UACAC;QACA,EACA;QACAN;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACA9B;QACAwB;QACAC;UACAM;UACAC;QACA,GACA;UACAD;UACAC;QACA,EACA;QACAN;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACA9B;QACAwB;QACAC;UACAM;UACAC;QACA;QACAN;QACAC;QACAC;QACAC;QACAC;MACA,EACA;MAEA;MACAT;MACAY;MACAC;MAEA;MACAC;MACAC;MAEA;MACAC;MACAC;MAEA;MACAC;MAEA;MACAC;MACAC;MAEA;MACAC;MAEA;MACAC;MAEA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;MACAC;MAAA;;MAEA;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;;MACAC;QACAJ;QACAC;QACAC;QACAC;MACA;MACAE;QACAL;QACAC;QACAC;QACAC;MACA;IACA;EACA;;EAEAG;IACAC;MAAA;MACA;MACA;;MAEA;MACA;QAAA;MAAA;;MAEA;MACA;MAEA;IACA;IAEAC;MACA;MACA;MACAC;MACAA;MACAA;MACA;IACA;IAEAC;MACA;IACA;IAEAC;MAAA;MACA;MACA;;MAEA;MACA;QACA;UACAC;QACA;QACAA;MACA;;MAEA;MACAC;QACA;QACAC;UAAA;QAAA;;QAEA;QACA;QACA;QAEA;UACAC;QACA;;QAEA;QACAA,kDACAC;UACAC;UACA3C;UACA4C;YAAA,uCACAC;cACAF;YAAA;UAAA,CACA;QAAA,GACA;MACA;MAEA;IACA;EACA;EACAG;IACA;;IAEA;IACA;MACAX;MACAY;QACAC;QACAC;QACAC;QACAC;QACAC;UACAL;YACAM;UACA;QACA;MACA;MACA;IACA;IAEA;IACA;IACA;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACA;IACAC;MACA;MACA;MACA;MAEA;;MAEA;MACA;MACA;MAEArB;QACA/D;QACAC;MACA;IACA;IAEA;IACAoF;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;QACAZ;UACAC;UACAC;UACAC;UACAC;QACA;QACA;MACA;MACAS;IACA;IAEA;IACAC;MACA;MACA;QACAd;UACAC;UACAC;UACAC;UACAC;QACA;QACA;MACA;MAEA;MAEA;MACA;QACAS;MACA;QACAA;MACA;IACA;IAEA;IACAE;MAAA;MACA;MAEA;MACA;MACA;MACA;MACA;;MAEA;MACA;;MAEA;MACA;MACA;MACA;MACA;;MAEA;MACA;QAAA;MAAA;;MAEA;MACA;QACAF;MACA;;MAEA;MACA;QACA;UACA;UACA;YACA;UACA;UACA;YACA;UACA;QACA;QACA;UACA;UACA;YACA;UACA;UACA;YACA;UACA;QACA;QACA;UACA;QACA;QACA;UACA;QACA;QACA;UACA;UACA;YACA;UACA;UACA;YACA;UACA;QACA;QACA;UACA;UACA;YACA;UACA;QACA;MACA;MAEAzB;;MAEA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MAEAY;QACAC;QACAe;MACA;IACA;IAIA;IACAC;MAAA;MACA;MACA;QACA7B;QACAY;UACAC;UACAC;UACAC;UACAC;UACAC;YACAL;cACAM;YACA;UACA;QACA;QACA;MACA;MAEA;QACAlB;QACA;MACA;MAEA;;MAEA;MACA;MACA;MACAA;MAEA;QACAkB;QACAD;UACAjB;QACA;QACA8B;UACA9B;UACA;UACA;QACA;MACA;MAEA;QACAA;QACA;QACA;;QAEAY;UACAC;UACAe;UACAG;QACA;;QAEA;QACA;MACA;MAEA;QACA;MACA;MAEA;QACA/B;QACA;QACAY;UACAC;UACAe;QACA;QACA;MACA;MAEA;QACA5B;QACA;QACA;;QAEAY;UACAC;UACAe;QACA;;QAEA;QACA;MACA;IACA;IAEA;IACAI;MAAA;MACA;QACAhC;QACAY;UACAC;UACAC;UACAC;UACAC;QACA;QACA;MACA;MAEA;MACA;;MAEAhB;MAEA;QACAA;QACA;MACA;IACA;IAEA;IACAiC;MAAA;MACA;;MAEA;QACA;UACA;YACAC;YACAC;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACAC;QACA;MACA;IACA;IAEAC;MACA;QACA;UACAtG;QACA;MACA;QACAgE;MACA;IACA;IAEA;IACAuC;MACA;QACA;UACA3B;YACAC;YACAe;YACAG;UACA;QACA;MACA;IACA;IAEAS;MACA;MACA;QACAC;QACA;MACA;;MAEA;MACA;;MAEA;MACA;QACA;QACA;MACA;;MAEA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;;QAEA;QACA;UACA;QACA;;QAEA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;;QAEA;QACA;UACA;YAAA;UAAA;UACA;YACA;YACAC;cACA7B;cACAqB;cACAS;YACA;UACA;UACA;QACA;;QAEA;QACA;UACA;UACA;UACA;QACA;;QAEA;QACA;UACA;YAAA;UAAA;UACA;YACAC;YACA;cACAA;YACA;YACA;YACA;cACAC;cACAhC;cACAiC;cACAC;cACAb;YACA;YACA;;YAEA;YACA;YACA;YACA;;YAEA;YACA;UACA;UACA;QACA;;QAEA;QACA;UACAnC;UACAA;UAEA;YAAA;UAAA;UACA;YACAiD;YACA;cACAA;YACA;;YAEA;YACA;;YAEA;YACA;UACA;UACA;QACA;;QAEA;QACA;;QAEA;QACA;MAEA;QACAjD;MACA;IACA;IAEAkD;MACA;MACA;QACA;QACA;UACA;UACA;QACA;UACA;UACA;UACA;QACA;QACA;QACA;MACA;MACA;MAAA,KACA;QACA;QACA;UACA;UACA;QACA;UACA;UACA;UACA;QACA;QACA;QACA;MACA;MACA;MAAA,KACA;QACAlD;QACA;QACA;UACA;UACA;UACAA;;UAEA;UACA;YAAA;UAAA;QAEA;UACA;UACA;UACAA;;UAEA;UACA;YAAA;UAAA;QAEA;QACA;QACA;MACA;MACA;MAAA,KACA;QACA;QACA;UACA;UACA;QACA;UACA;UACA;UACA;QACA;QACA;QACA;MACA;IACA;IAEAmD;MACA;;MAEA;MACA;QACA;UACAnD;UACA2C;YAAA;UAAA;UACA;QACA;UACA3C;UACA2C;YAAA;UAAA;UACA;QACA;UACA3C;UACA2C;YAAA;UAAA;UACA;QACA;UACA3C;UACA2C;YAAA;UAAA;UACA;QACA;UACA3C;UACA2C;YAAA;UAAA;UACA;QACA;UACA3C;UACA2C;YAAA;UAAA;UACA;UACA;YACAA;cACA5G;cACAwB;cACAC;gBACAM;gBACAC;cACA,GACA;gBACAD;gBACAC;cACA;cACAN;cACAC;cACAC;cACAC;gBACAkD;gBACAqB;gBACAS;cACA;cACA/E;YACA;YACA;UACA;UACA;MAAA;MAGA;QACA;QACA8E;;QAEA;QACA;UACAA;QACA;;QAEA;QACA;UAAA;QAAA;QACA;UACA;YACAG;YACAhC;YACAiC;YACAC;YACAb;UACA;UACA;QACA;UACA;UACA;YACAW;YACAhC;YACAiC;YACAC;YACAb;UACA;UACA;QACA;;QAEA;QACA;QACA;QACA;;QAEA;QACA;;QAEA;QACA;MACA;IACA;IAEA;IACAiB;MACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEAC;MACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEAC;MACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA9B;IACA;IAEA;IACA+B;MACA;IACA;IAEA;IACAC;MACA7C;QACA8C;QACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEAC;MACA;QACA;QACA;UACA;UACA;YACA;UACA;;UACA;UACA;QACA;QACA;MACA;QACA;MACA;IACA;IAEAC;MACA;MACA;MACA;MACA;QAAA;MAAA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEAC;MACApD;QACA5E;QACAiF;UACAL;YACAC;YACAe;UACA;QACA;MACA;IACA;IAIA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEAqC;MACA;MACA;IACA;IAEAC;MACAtD;QACA5E;QACAiF;UACAL;YACAC;YACAe;UACA;QACA;QACAE;UACAlB;YACAC;YACAe;UACA;QACA;MACA;IACA;IAEA;IACAuC;MACAvD;QACA5E;QACAiF;UACAL;YACAC;YACAe;UACA;QACA;QACAE;UACAlB;YACAC;YACAe;UACA;QACA;MACA;IACA;IAEA;IACAwC;MACAxD;QACAC;MACA;;MAEA;MACAD;QACAM;QACAD;UACAL;UACA;YACA;YACAA;cACAyD;cACApD;gBACAL;kBACAC;kBACAe;gBACA;cACA;cACAE;gBACA;gBACAlB;kBACAC;kBACAC;kBACAC;kBACAE;oBACAL;sBACA5E;oBACA;kBACA;gBACA;cACA;YACA;UACA;YACA4E;cACAC;cACAe;YACA;UACA;QACA;QACAE;UACAlB;UACA;UACAA;YACAC;YACAC;YACAC;YACAE;cACAL;gBACA5E;cACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAsI;MACA;MACA;IACA;IAEAC;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAzE;gBACAY;kBACAC;kBACAe;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA8C;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;;QAEA;QACA;QACA;QACA;QACA;QAEA;QACA9D;UACAC;UACAe;QACA;MACA;QACA5B;QACAY;UACAC;UACAe;QACA;MACA;IACA;IAEA;IACA+C;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAF;gBACA;kBACA;kBACAG;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA5E;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA6E;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;kBACAxH;kBACAF;kBACAa;kBACAC;kBACAE;kBACAjC;kBACAM;kBACAC;kBACAC;gBACA;gBAAA;gBAAA;gBAAA,OAGA;kBACAV;kBACAK;kBACAN;kBACAG;kBACAM;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAqD;gBACAY;kBACAC;kBACAe;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAmD;MACA;QACA/E;QAEA;UACA;QACA;QAEA;QAEA;UACAgF;QACA;UACA;UACA;UACA;YACA;cAAAC;cAAAC;cAAAC;cAAAC;cAAAC;cAAAC;YACAN,gBACAO,gBACAA,qBACAA,eACAA,gBACAA,kBACAA,iBACA;UACA;YACA;YACA;YACAP;YAEA;cACAA;YACA;UACA;QACA;UACAA;QACA;QAEAhF;QAEA;UACA;QACA;QAEA;QACA;QACAwF;QAEA;UACA;QACA;UACA;QACA;UACA;QACA;MACA;QACAxF;QACA;MACA;IACA;IAEA;IACAyF;MACA;QACAzF;QAEA;QAEA;UACA;QACA;;QAEA;QACA;UACAgF;QACA;UACA;UACA;YACAA;UACA;UACA;UAAA,KACA;YACA;YACA;cACA;gBAAAC;gBAAAC;gBAAAC;gBAAAC;gBAAAC;gBAAAC;cACA;cACAN,gBACAO,gBACAA,qBACAA,eACAA,iBACAA,mBACAA,iBACA;YACA;cACA;cACA;cACAP;cAEA;gBACAA;cACA;YACA;UACA;QACA;UACAA;QACA;UACAA;QACA;QAEAhF;QAEA;UACA;QACA;;QAEA;QACA;QACA;QAEA;QAEAA;QACA;MAEA;QACAA;QACA;MACA;IACA;IAEA;IACA0F;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEAC;MACA;IACA;IAEA;IACAC;MACA;QACAjF;UACAC;UACAe;QACA;QACA;MACA;MACA5B;MACA;MACA;QACA8C;QACAhC;QACAiC;QACAC;QACAb;MACA;MACAnC;;MAGA;MACA,qrCAOA;MACA;IACA;IAEA8F;MACA;IACA;IAEAC;MACA;;MAEA;MACA;QACAjJ;QACAC;QACAC;QACAC;UACAV;UACAN;UACAC;UACAI;UACAE;QACA;MACA;;MAEA;MACAwD;MACA;MACA;;MAEA;MACA;QACAjE;QACAwB;QACAC;QACAC;QACAC;QACAC;QACAC,eACA;UACAkD;UACAqB;UACAS;UACAV;QACA,EACA;QACArE;MACA;;MAEA;MACA;QAAA;MAAA;MACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;QACA;MACA;MAEA+C;QACAC;QACAe;MACA;IACA;IAEA;IACAoE;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAhG;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBACAA;gBACAY;kBACAC;kBACAe;gBACA;gBAAA;cAAA;gBAIAhB;kBACAC;gBACA;;gBAEA;gBACA;gBAEA5D;kBACAgJ;kBACAhK;kBACA8G;kBACAD;kBACAoD;gBACA;gBAEAlG;gBAAA;gBAAA,OAEA;cAAA;gBAAAyE;gBAEA7D;gBAEA;kBACAA;oBACAC;oBACAe;kBACA;gBACA;kBACAhB;oBACAC;oBACAe;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAhB;gBACAZ;gBACAY;kBACAC;kBACAe;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAuE;MACA;MACAnG;MACAA;MACA;MACAA;MACAA;IACA;IAEAoG;MAAA;MACA;;MAEA;MACA,oCACAC;QAAA;MAAA,GACAC;QACA;QACA;QACA;MACA,GACAC;;MAEA;MACA;;MAEA;MACA;QACAzJ;QACAC;QACAC;QACAC;UACAV;UACAN;UACAC;UACAI;UACAE;QACA;MACA;;MAEA;MACAwD;MACA;MACA;;MAEA;MACA;QACAjE;QACAwB;QACAC;QACAC;QACAC;QACAC;QACAC,eACA;UACAkD;UACAqB;UACAS;UACAV;QACA,EACA;QACArE;MACA;;MAEA;MACA;QAAA;MAAA;MACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;QACA;MACA;MAEA+C;QACAC;QACAe;MACA;IACA;IAEA;IACA4E;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAlK;QACAL;QACAC;QACAK;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACA;MACA;QACAb;QACAwB;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACA9B;QACAwB;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACA9B;QACAwB;QACAC;UACAM;UACAC;QACA,GACA;UACAD;UACAC;QACA,EACA;QACAN;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACA9B;QACAwB;QACAC;UACAM;UACAC;QACA,GACA;UACAD;UACAC;QACA,EACA;QACAN;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACA9B;QACAwB;QACAC;UACAM;UACAC;QACA,GACA;UACAD;UACAC;QACA,EACA;QACAN;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACA9B;QACAwB;QACAC;UACAM;UACAC;QACA;QACAN;QACAC;QACAC;QACAC;QACAC;MACA,EACA;MACA;;MAEA;MACA;MACA;MACA;MAEA+C;QACAC;QACAe;MACA;IACA;IAEA;IACA6E;MAAA;MACA;MACAC;QACA;QACA;MACA;IACA;IAEAC;MACA;MACA;QACAzE;QACAjG;QACAC;MACA;;MAEA;MACA;QACAgG;QACAjG;QACAC;MACA;;MAEA;MACA;QACAgG;QACAjG;QACAC;MACA;;MAGA;MACA;QACAgG;QACAjG;QACAC;MACA;IACA;IAEA0K;MACA;QACArH;QACAC;QACAC;MACA;MACA;IACA;IAEAoH;MACA;QACAtH;QACAC;QACAC;MACA;MACA;IACA;IAMAqH;MAAA;MACA;MACA;QACAvH;QACAC;QACAC;QACAC;MACA;;MAEA;MACA;QACAH;QACAC;QACAC;QACAC;MACA;;MAEA;MACA;QACAH;QACAC;QACAC;QACAC;MACA;;MAEA;MACAkB;QACAC;QACAe;QACAG;MACA;;MAEA;MACA;MACA2E;QACA;QACA;QACAA;UACA;QACA;MACA;IACA;IAEA;IACAK;MACA;QACA;QACA;UACA;QAAA;QACA;QACA;UACA;QAAA;QACA;UACA;QAAA;QACA;UACA;QAAA;QACA;UACA;MAAA;IAEA;IAEA;IACAC;MACA;QACA;QACA;UACA;QACA;QACA;UACA;QACA;UACA;QACA;UACA;QAAA;QACA;UACA;MAAA;IAEA;IAEA;IACAC;MACA;MACAjH;IACA;IAEA;IACAkH;MACA;MACAlH;IACA;IAEA;IACAmH;MACA;QACAnH;QAEA;UACA;QACA;QAEA;QAEA;UACAgF;QACA;UACA;UACA;YACAA;UACA;UACA;UAAA,KACA;YACA;YACA;cACA;gBAAAC;gBAAAC;gBAAAC;gBAAAC;gBAAAC;gBAAAC;cACAN,gBACAO,gBACAA,qBACAA,eACAA,kBACAA,oBACAA,kBACA;YACA;cACA;cACA;cACAP;cAEA;gBACAA;cACA;YACA;UACA;QACA;UACAA;QACA;UACAA;QACA;QAEAhF;QAEA;UACA;QACA;;QAEA;QACA;QACA;QACA;QAEA;QAEAA;QACA;MAEA;QACAA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACrtEA;AAAA;AAAA;AAAA;AAA6tC,CAAgB,wnCAAG,EAAC,C;;;;;;;;;;;ACAjvC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/work/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './pages/work/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=51b5538d&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=51b5538d&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"51b5538d\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/work/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=51b5538d&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l1 = _vm.sectionExpanded.aiConfig\n    ? _vm.__map(_vm.aiList, function (ai, index) {\n        var $orig = _vm.__get_orig(ai)\n        var m0 = ai.enabled && _vm.isAiLoginEnabled(ai)\n        var m1 = _vm.isAiLoginEnabled(ai)\n        var m2 = _vm.isAiLoginEnabled(ai)\n        var m3 =\n          !_vm.isAiLoginEnabled(ai) &&\n          !_vm.isLoading.yuanbao &&\n          !_vm.isLoading.doubao &&\n          !_vm.isLoading.agent\n        var m4 = _vm.isAiInLoading(ai)\n        var m5 = ai.enabled && _vm.isAiLoginEnabled(ai)\n        var m6 = !_vm.isAiLoginEnabled(ai) || _vm.isAiInLoading(ai)\n        var g0 = ai.capabilities.length\n        var l0 =\n          g0 > 0\n            ? _vm.__map(ai.capabilities, function (capability, __i0__) {\n                var $orig = _vm.__get_orig(capability)\n                var g1 = ai.selectedCapabilities.includes(capability.value)\n                var m7 = !ai.enabled || !_vm.isAiLoginEnabled(ai)\n                return {\n                  $orig: $orig,\n                  g1: g1,\n                  m7: m7,\n                }\n              })\n            : null\n        return {\n          $orig: $orig,\n          m0: m0,\n          m1: m1,\n          m2: m2,\n          m3: m3,\n          m4: m4,\n          m5: m5,\n          m6: m6,\n          g0: g0,\n          l0: l0,\n        }\n      })\n    : null\n  var g2 = _vm.sectionExpanded.promptInput ? _vm.promptInput.length : null\n  var l3 =\n    _vm.taskStarted && _vm.sectionExpanded.taskStatus\n      ? _vm.__map(_vm.enabledAIs, function (ai, index) {\n          var $orig = _vm.__get_orig(ai)\n          var m8 = _vm.getStatusText(ai.status)\n          var m9 = _vm.getStatusIconClass(ai.status)\n          var m10 = _vm.getStatusEmoji(ai.status)\n          var g3 = ai.isExpanded && ai.progressLogs.length > 0\n          var l2 = g3\n            ? _vm.__map(ai.progressLogs, function (log, logIndex) {\n                var $orig = _vm.__get_orig(log)\n                var m11 = _vm.formatTime(log.timestamp)\n                return {\n                  $orig: $orig,\n                  m11: m11,\n                }\n              })\n            : null\n          return {\n            $orig: $orig,\n            m8: m8,\n            m9: m9,\n            m10: m10,\n            g3: g3,\n            l2: l2,\n          }\n        })\n      : null\n  var g4 = _vm.results.length\n  var m12 =\n    g4 > 0 && _vm.currentResult\n      ? _vm.currentResult.shareImgUrl &&\n        _vm.isImageFile(_vm.currentResult.shareImgUrl)\n      : null\n  var m13 =\n    g4 > 0 && _vm.currentResult && !m12\n      ? _vm.currentResult.shareImgUrl &&\n        _vm.isPdfFile(_vm.currentResult.shareImgUrl)\n      : null\n  var m14 =\n    g4 > 0 &&\n    _vm.currentResult &&\n    !m12 &&\n    !m13 &&\n    !(_vm.currentResult.aiName === \"DeepSeek\")\n      ? _vm.renderMarkdown(_vm.currentResult.content)\n      : null\n  var l5 = _vm.historyDrawerVisible\n    ? _vm.__map(_vm.groupedHistory, function (group, date) {\n        var $orig = _vm.__get_orig(group)\n        var l4 = _vm.__map(group, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m15 = _vm.formatHistoryTime(item.createTime)\n          return {\n            $orig: $orig,\n            m15: m15,\n          }\n        })\n        return {\n          $orig: $orig,\n          l4: l4,\n        }\n      })\n    : null\n  var l6 = _vm.scoreModalVisible\n    ? _vm.__map(_vm.results, function (result, index) {\n        var $orig = _vm.__get_orig(result)\n        var g5 = _vm.selectedResults.includes(result.aiName)\n        return {\n          $orig: $orig,\n          g5: g5,\n        }\n      })\n    : null\n  var g6 = _vm.layoutModalVisible ? _vm.layoutPrompt.trim().length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l1: l1,\n        g2: g2,\n        l3: l3,\n        g4: g4,\n        m12: m12,\n        m13: m13,\n        m14: m14,\n        l5: l5,\n        l6: l6,\n        g6: g6,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"console-container\">\n\t\t<!-- 顶部固定区域 -->\n\t\t<view class=\"header-fixed\">\n\t\t\t<view class=\"header-content\">\n\t\t\t\t<text class=\"header-title\">AI控制台</text>\n\t\t\t\t<view class=\"header-actions\">\n\t\t\t\t\t<view class=\"action-btn refresh-btn\" @tap=\"refreshAiStatus\">\n\t\t\t\t\t\t<image class=\"action-icon-img\" src=\"https://u3w.com/chatfile/shuaxin.png\" mode=\"aspectFit\">\n\t\t\t\t\t\t</image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"action-btn history-btn\" @tap=\"showHistoryDrawer\">\n\t\t\t\t\t\t<image class=\"action-icon-img\" src=\"https://u3w.com/chatfile/lishi.png\" mode=\"aspectFit\">\n\t\t\t\t\t\t</image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"action-btn new-chat-btn\" @tap=\"createNewChat\">\n\t\t\t\t\t\t<image class=\"action-icon-img\" src=\"https://u3w.com/chatfile/chuangjian.png\" mode=\"aspectFit\">\n\t\t\t\t\t\t</image>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\n\t\t</view>\n\n\t\t<!-- 主体滚动区域 -->\n\t\t<scroll-view class=\"main-scroll\" scroll-y :scroll-into-view=\"scrollIntoView\" :enhanced=\"true\" :bounces=\"true\"\n\t\t\t:show-scrollbar=\"false\" :fast-deceleration=\"false\">\n\n\t\t\t<!-- AI配置区块 -->\n\t\t\t<view class=\"section-block\" id=\"ai-config\">\n\t\t\t\t<view class=\"section-header\" @tap=\"toggleSection('aiConfig')\">\n\t\t\t\t\t<text class=\"section-title\">AI选择配置</text>\n\t\t\t\t\t<text class=\"section-arrow\">\n\t\t\t\t\t\t{{ sectionExpanded.aiConfig ? '▼' : '▶' }}\n\t\t\t\t\t</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"section-content\" v-if=\"sectionExpanded.aiConfig\">\n\t\t\t\t\t<view class=\"ai-grid\">\n\t\t\t\t\t\t<view v-for=\"(ai, index) in aiList\" :key=\"index\" class=\"ai-card\"\n\t\t\t\t\t\t\t:class=\"[ai.enabled && isAiLoginEnabled(ai) ? 'ai-enabled' : '', !isAiLoginEnabled(ai) ? 'ai-disabled' : '']\">\n\t\t\t\t\t\t\t<view class=\"ai-header\">\n\t\t\t\t\t\t\t\t<!-- <image class=\"ai-avatar\" :src=\"ai.avatar\" mode=\"aspectFill\" :class=\"[!isAiLoginEnabled(ai) ? 'avatar-disabled' : '']\"></image> -->\n\t\t\t\t\t\t\t\t<view class=\"ai-info\">\n\t\t\t\t\t\t\t\t\t<view class=\"ai-name-container\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"ai-name\" :class=\"[!isAiLoginEnabled(ai) ? 'name-disabled' : '']\">{{\n\t\t\t\t\t\t\t\t\t\t\tai.name }}</text>\n\t\t\t\t\t\t\t\t\t\t<text\n\t\t\t\t\t\t\t\t\t\t\tv-if=\"!isAiLoginEnabled(ai) && !isLoading.yuanbao && !isLoading.doubao && !isLoading.agent\"\n\t\t\t\t\t\t\t\t\t\t\tclass=\"login-required\">需登录</text>\n\t\t\t\t\t\t\t\t\t\t<text v-if=\"isAiInLoading(ai)\" class=\"loading-text\">检查中...</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<switch :checked=\"ai.enabled && isAiLoginEnabled(ai)\"\n\t\t\t\t\t\t\t\t\t\t:disabled=\"!isAiLoginEnabled(ai) || isAiInLoading(ai)\"\n\t\t\t\t\t\t\t\t\t\t@change=\"toggleAI(ai, $event)\" color=\"#409EFF\" style=\"transform: scale(0.8);\" />\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"ai-capabilities\" v-if=\"ai.capabilities.length > 0\">\n\t\t\t\t\t\t\t\t<view v-for=\"capability in ai.capabilities\" :key=\"capability.value\"\n\t\t\t\t\t\t\t\t\tclass=\"capability-tag\"\n\t\t\t\t\t\t\t\t\t:class=\"[ai.selectedCapabilities.includes(capability.value) ? 'capability-active' : '', (!ai.enabled || !isAiLoginEnabled(ai)) ? 'capability-disabled' : '']\"\n\t\t\t\t\t\t\t\t\t@tap=\"toggleCapability(ai, capability.value)\">\n\t\t\t\t\t\t\t\t\t<text class=\"capability-text\">{{ capability.label }}</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 提示词输入区块 -->\n\t\t\t<view class=\"section-block\" id=\"prompt-input\">\n\t\t\t\t<view class=\"section-header\" @tap=\"toggleSection('promptInput')\">\n\t\t\t\t\t<text class=\"section-title\">提示词输入</text>\n\t\t\t\t\t<text class=\"section-arrow\">\n\t\t\t\t\t\t{{ sectionExpanded.promptInput ? '▼' : '▶' }}\n\t\t\t\t\t</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"section-content\" v-if=\"sectionExpanded.promptInput\">\n\t\t\t\t\t<textarea class=\"prompt-textarea\" v-model=\"promptInput\" placeholder=\"请输入提示词\" maxlength=\"2000\"\n\t\t\t\t\t\tshow-confirm-bar=\"false\" auto-height></textarea>\n\t\t\t\t\t<view class=\"prompt-footer\">\n\t\t\t\t\t\t<text class=\"word-count\">{{ promptInput.length }}/2000</text>\n\t\t\t\t\t\t<button class=\"send-btn\" :class=\"[!canSend ? 'send-btn-disabled' : '']\" :disabled=\"!canSend\"\n\t\t\t\t\t\t\t@tap=\"sendPrompt\">\n\t\t\t\t\t\t\t发送\n\t\t\t\t\t\t</button>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 执行状态区块 -->\n\t\t\t<view class=\"section-block\" v-if=\"taskStarted\" id=\"task-status\">\n\t\t\t\t<view class=\"section-header\" @tap=\"toggleSection('taskStatus')\">\n\t\t\t\t\t<text class=\"section-title\">任务执行状态</text>\n\t\t\t\t\t<text class=\"section-arrow\">\n\t\t\t\t\t\t{{ sectionExpanded.taskStatus ? '▼' : '▶' }}\n\t\t\t\t\t</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"section-content\" v-if=\"sectionExpanded.taskStatus\">\n\t\t\t\t\t<!-- 任务流程 -->\n\t\t\t\t\t<view class=\"task-flow\">\n\t\t\t\t\t\t<view v-for=\"(ai, index) in enabledAIs\" :key=\"index\" class=\"task-item\">\n\t\t\t\t\t\t\t<view class=\"task-header\" @tap=\"toggleTaskExpansion(ai)\">\n\t\t\t\t\t\t\t\t<view class=\"task-left\">\n\t\t\t\t\t\t\t\t\t<text class=\"task-arrow\">\n\t\t\t\t\t\t\t\t\t\t{{ ai.isExpanded ? '▼' : '▶' }}\n\t\t\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t\t\t\t<image class=\"task-avatar\" :src=\"ai.avatar\" mode=\"aspectFill\"></image>\n\t\t\t\t\t\t\t\t\t<text class=\"task-name\">{{ ai.name }}</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"task-right\">\n\t\t\t\t\t\t\t\t\t<text class=\"status-text\">{{ getStatusText(ai.status) }}</text>\n\t\t\t\t\t\t\t\t\t<text class=\"status-icon\" :class=\"[getStatusIconClass(ai.status)]\">\n\t\t\t\t\t\t\t\t\t\t{{ getStatusEmoji(ai.status) }}\n\t\t\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<!-- 进度日志 -->\n\t\t\t\t\t\t\t<view class=\"progress-logs\" v-if=\"ai.isExpanded && ai.progressLogs.length > 0\">\n\t\t\t\t\t\t\t\t<view v-for=\"(log, logIndex) in ai.progressLogs\" :key=\"logIndex\" class=\"progress-item\">\n\t\t\t\t\t\t\t\t\t<view class=\"progress-dot\" :class=\"[log.isCompleted ? 'dot-completed' : '']\"></view>\n\t\t\t\t\t\t\t\t\t<view class=\"progress-content\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"progress-time\">{{ formatTime(log.timestamp) }}</text>\n\t\t\t\t\t\t\t\t\t\t<text class=\"progress-text\">{{ log.content }}</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- 主机可视化 -->\n\t\t\t\t\t<!-- \t<view class=\"screenshots-section\" v-if=\"screenshots.length > 0\">\n\t\t\t\t\t\t<view class=\"screenshots-header\">\n\t\t\t\t\t\t\t<text class=\"section-subtitle\">主机可视化</text>\n\t\t\t\t\t\t\t<switch :checked=\"autoPlay\" @change=\"toggleAutoPlay\" color=\"#409EFF\"\n\t\t\t\t\t\t\t\tstyle=\"transform: scale(0.8);\" />\n\t\t\t\t\t\t\t<text class=\"auto-play-text\">自动轮播</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<swiper class=\"screenshots-swiper\" :autoplay=\"autoPlay\" :interval=\"3000\" :duration=\"500\"\n\t\t\t\t\t\t\tindicator-dots indicator-color=\"rgba(255,255,255,0.5)\" indicator-active-color=\"#409EFF\">\n\t\t\t\t\t\t\t<swiper-item v-for=\"(screenshot, index) in screenshots\" :key=\"index\">\n\t\t\t\t\t\t\t\t<image class=\"screenshot-image\" :src=\"screenshot\" mode=\"aspectFit\"\n\t\t\t\t\t\t\t\t\t@tap=\"previewImage(screenshot)\"></image>\n\t\t\t\t\t\t\t</swiper-item>\n\t\t\t\t\t\t</swiper>\n\t\t\t\t\t</view> -->\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 结果展示区块 -->\n\t\t\t<view class=\"section-block\" v-if=\"results.length > 0\" id=\"results\">\n\t\t\t\t<view class=\"section-header\">\n\t\t\t\t\t<text class=\"section-title\">执行结果</text>\n\t\t\t\t\t<button class=\"score-btn\" size=\"mini\" @tap=\"showScoreModal\">智能评分</button>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"section-content\">\n\t\t\t\t\t<!-- 结果选项卡 -->\n\t\t\t\t\t<scroll-view class=\"result-tabs\" scroll-x>\n\t\t\t\t\t\t<view class=\"tab-container\">\n\t\t\t\t\t\t\t<view v-for=\"(result, index) in results\" :key=\"index\" class=\"result-tab\"\n\t\t\t\t\t\t\t\t:class=\"[activeResultIndex === index ? 'tab-active' : '']\"\n\t\t\t\t\t\t\t\t@tap=\"switchResultTab(index)\">\n\t\t\t\t\t\t\t\t<text class=\"tab-text\">{{ result.aiName }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</scroll-view>\n\n\t\t\t\t\t<!-- 结果内容 -->\n\t\t\t\t\t<view class=\"result-content\" v-if=\"currentResult\">\n\t\t\t\t\t\t<!-- 结果标题 -->\n\t\t\t\t\t\t<!-- <view class=\"result-header\">\n\t\t\t\t\t\t\t<text class=\"result-title\">{{ currentResult.aiName }}的执行结果</text>\n\t\t\t\t\t\t</view> -->\n\n\t\t\t\t\t\t<!-- 操作按钮 -->\n\t\t\t\t\t\t<view class=\"result-actions\">\n\t\t\t\t\t\t\t<button class=\"share-link-btn\" size=\"mini\" v-if=\"currentResult.shareUrl\"\n\t\t\t\t\t\t\t\t@tap=\"openShareUrl(currentResult.shareUrl)\">\n\t\t\t\t\t\t\t\t复制原链接\n\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t\t<button class=\"action-btn-small\" size=\"mini\"\n\t\t\t\t\t\t\t\t@tap=\"copyResult(currentResult.content)\">复制(纯文本)</button>\n\t\t\t\t\t\t\t<button class=\"collect-btn\" size=\"mini\"\n\t\t\t\t\t\t\t\t@tap=\"showLayoutModal\">投递到公众号</button>\n\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t<!-- 分享图片或内容 -->\n\t\t\t\t\t\t<view class=\"result-body\">\n\t\t\t\t\t\t\t<!-- 图片内容 -->\n\t\t\t\t\t\t\t<view v-if=\"currentResult.shareImgUrl && isImageFile(currentResult.shareImgUrl)\"\n\t\t\t\t\t\t\t\tclass=\"result-image-container\">\n\t\t\t\t\t\t\t\t<image class=\"result-image\" :src=\"currentResult.shareImgUrl\" mode=\"widthFix\"\n\t\t\t\t\t\t\t\t\t@tap=\"previewImage(currentResult.shareImgUrl)\"></image>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<!-- PDF文件内容 -->\n\t\t\t\t\t\t\t<view v-else-if=\"currentResult.shareImgUrl && isPdfFile(currentResult.shareImgUrl)\"\n\t\t\t\t\t\t\t\tclass=\"result-pdf-container\">\n\t\t\t\t\t\t\t\t<view class=\"pdf-placeholder\">\n\t\t\t\t\t\t\t\t\t<view class=\"pdf-icon\">📄</view>\n\t\t\t\t\t\t\t\t\t<text class=\"pdf-text\">PDF文件</text>\n\t\t\t\t\t\t\t\t\t<view class=\"pdf-actions\">\n\t\t\t\t\t\t\t\t\t\t<button class=\"pdf-btn download-btn\" size=\"mini\"\n\t\t\t\t\t\t\t\t\t\t\t@tap=\"openPdfFile(currentResult.shareImgUrl)\">\n\t\t\t\t\t\t\t\t\t\t\t打开文件\n\t\t\t\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t\t\t\t\t<button class=\"pdf-btn copy-btn\" size=\"mini\"\n\t\t\t\t\t\t\t\t\t\t\t@tap=\"copyPdfUrl(currentResult.shareImgUrl)\">\n\t\t\t\t\t\t\t\t\t\t\t复制链接\n\t\t\t\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n              <!-- 文字内容 -->\n              <view v-else class=\"result-text\">\n                <!-- 特殊处理DeepSeek响应 -->\n                <rich-text v-if=\"currentResult.aiName === 'DeepSeek'\" :nodes=\"currentResult.content\"></rich-text>\n                <rich-text v-else :nodes=\"renderMarkdown(currentResult.content)\"></rich-text>\n              </view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</scroll-view>\n\n\t\t<!-- 历史记录抽屉 -->\n\t\t<view v-if=\"historyDrawerVisible\" class=\"drawer-mask\" @tap=\"closeHistoryDrawer\">\n\t\t\t<view class=\"drawer-container\" @tap.stop>\n\t\t\t\t<view class=\"drawer-content\">\n\t\t\t\t\t<view class=\"drawer-header\">\n\t\t\t\t\t\t<text class=\"drawer-title\">历史会话记录</text>\n\t\t\t\t\t\t<text class=\"close-icon\" @tap=\"closeHistoryDrawer\">✕</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<scroll-view class=\"history-list\" scroll-y>\n\t\t\t\t\t\t<view v-for=\"(group, date) in groupedHistory\" :key=\"date\" class=\"history-group\">\n\t\t\t\t\t\t\t<text class=\"history-date\">{{ date }}</text>\n\t\t\t\t\t\t\t<view v-for=\"(item, index) in group\" :key=\"index\" class=\"history-item\"\n\t\t\t\t\t\t\t\t@tap=\"loadHistoryItem(item)\">\n\t\t\t\t\t\t\t\t<text class=\"history-prompt\">{{ item.userPrompt }}</text>\n\t\t\t\t\t\t\t\t<text class=\"history-time\">{{ formatHistoryTime(item.createTime) }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</scroll-view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 智能评分弹窗 -->\n\t\t<view v-if=\"scoreModalVisible\" class=\"popup-mask\" @tap=\"closeScoreModal\">\n\t\t\t<view class=\"score-modal\" @tap.stop>\n\t\t\t\t<view class=\"score-header\">\n\t\t\t\t\t<text class=\"score-title\">智能评分</text>\n\t\t\t\t\t<text class=\"close-icon\" @tap=\"closeScoreModal\">✕</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"score-content\">\n\t\t\t\t\t<view class=\"score-prompt-section\">\n\t\t\t\t\t\t<text class=\"score-subtitle\">评分提示词：</text>\n\t\t\t\t\t\t<textarea class=\"score-textarea\" v-model=\"scorePrompt\"\n\t\t\t\t\t\t\tplaceholder=\"请输入评分提示词，例如：请从内容质量、逻辑性、创新性等方面进行评分\" maxlength=\"1000\"></textarea>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"score-selection\">\n\t\t\t\t\t\t<text class=\"score-subtitle\">选择要评分的内容：</text>\n\t\t\t\t\t\t<checkbox-group @change=\"toggleResultSelection\">\n\t\t\t\t\t\t\t<view class=\"score-checkboxes\">\n\t\t\t\t\t\t\t\t<label v-for=\"(result, index) in results\" :key=\"index\" class=\"checkbox-item\">\n\t\t\t\t\t\t\t\t\t<checkbox :value=\"result.aiName\"\n\t\t\t\t\t\t\t\t\t\t:checked=\"selectedResults.includes(result.aiName)\" />\n\t\t\t\t\t\t\t\t\t<text class=\"checkbox-text\">{{ result.aiName }}</text>\n\t\t\t\t\t\t\t\t</label>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</checkbox-group>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<button class=\"score-submit-btn\" :disabled=\"!canScore\" @tap=\"handleScore\">\n\t\t\t\t\t\t开始评分\n\t\t\t\t\t</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 智能排版弹窗 -->\n\t\t<view v-if=\"layoutModalVisible\" class=\"popup-mask\" @tap=\"closeLayoutModal\">\n\t\t\t<view class=\"score-modal\" @tap.stop>\n\t\t\t\t<view class=\"score-header\">\n\t\t\t\t\t<text class=\"score-title\">智能排版</text>\n\t\t\t\t\t<text class=\"close-icon\" @tap=\"closeLayoutModal\">✕</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"score-content\">\n\t\t\t\t\t<view class=\"score-prompt-section\">\n\t\t\t\t\t\t<text class=\"score-subtitle\">排版提示词：</text>\n\t\t\t\t\t\t<textarea class=\"score-textarea\" v-model=\"layoutPrompt\"\n\t\t\t\t\t\t\tplaceholder=\"请输入排版要求\" maxlength=\"100000\"></textarea>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<button class=\"score-submit-btn\" :disabled=\"layoutPrompt.trim().length === 0\" @tap=\"handleLayout\">\n\t\t\t\t\t\t开始智能排版\n\t\t\t\t\t</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport {\n\t\tmarked\n\t} from 'marked';\n\timport {\n\t\tmessage, saveUserChatData, getChatHistory,pushAutoOffice\n\t} from \"@/api/wechat/aigc\";\n\timport {\n\t\tv4 as uuidv4\n\t} from 'uuid';\n\timport storage from '@/utils/storage'\n\timport constant from '@/utils/constant'\n\n\n\texport default {\n\t\tname: 'MiniConsole',\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\t// 用户信息\n\t\t\t\tuserId: '',\n\t\t\t\tcorpId: '',\n\t\t\t\tchatId: '',\n\t\t\t\texpandedHistoryItems: {},\n\t\t\t\tuserInfoReq: {\n\t\t\t\t\tuserPrompt: '',\n\t\t\t\t\tuserId: '',\n\t\t\t\t\tcorpId: '',\n\t\t\t\t\ttaskId: '',\n\t\t\t\t\troles: '',\n\t\t\t\t\ttoneChatId: '',\n\t\t\t\t\tybDsChatId: '',\n\t\t\t\t\tdbChatId: '',\n\t\t\t\t\tisNewChat: true\n\t\t\t\t},\n\t\t\t\tjsonRpcReqest: {\n\t\t\t\t\tjsonrpc: '2.0',\n\t\t\t\t\tid: '',\n\t\t\t\t\tmethod: '',\n\t\t\t\t\tparams: {}\n\t\t\t\t},\n\n\t\t\t\t// 区域展开状态\n\t\t\t\tsectionExpanded: {\n\t\t\t\t\taiConfig: true,\n\t\t\t\t\tpromptInput: true,\n\t\t\t\t\ttaskStatus: true\n\t\t\t\t},\n\n\t\t\t\t// AI配置（参考PC端完整配置）\n\t\t\t\taiList: [{\n\t\t\t\t\t\tname: 'AI搜索@元器',\n\t\t\t\t\t\tavatar: 'https://u3w.com/chatfile/yuanbao.png',\n\t\t\t\t\t\tcapabilities: [],\n\t\t\t\t\t\tselectedCapabilities: [],\n\t\t\t\t\t\tenabled: true,\n\t\t\t\t\t\tstatus: 'idle',\n\t\t\t\t\t\tprogressLogs: [],\n\t\t\t\t\t\tisExpanded: true\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: '数智化助手@元器',\n\t\t\t\t\t\tavatar: 'https://u3w.com/chatfile/yuanbao.png',\n\t\t\t\t\t\tcapabilities: [],\n\t\t\t\t\t\tselectedCapabilities: [],\n\t\t\t\t\t\tenabled: true,\n\t\t\t\t\t\tstatus: 'idle',\n\t\t\t\t\t\tprogressLogs: [],\n\t\t\t\t\t\tisExpanded: true\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: '腾讯元宝T1',\n\t\t\t\t\t\tavatar: 'https://u3w.com/chatfile/yuanbao.png',\n\t\t\t\t\t\tcapabilities: [{\n\t\t\t\t\t\t\t\tlabel: '深度思考',\n\t\t\t\t\t\t\t\tvalue: 'deep_thinking'\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tlabel: '联网搜索',\n\t\t\t\t\t\t\t\tvalue: 'web_search'\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t],\n\t\t\t\t\t\tselectedCapabilities: ['deep_thinking', 'web_search'],\n\t\t\t\t\t\tenabled: true,\n\t\t\t\t\t\tstatus: 'idle',\n\t\t\t\t\t\tprogressLogs: [],\n\t\t\t\t\t\tisExpanded: true\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: '腾讯元宝DS',\n\t\t\t\t\t\tavatar: 'https://u3w.com/chatfile/yuanbao.png',\n\t\t\t\t\t\tcapabilities: [{\n\t\t\t\t\t\t\t\tlabel: '深度思考',\n\t\t\t\t\t\t\t\tvalue: 'deep_thinking'\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tlabel: '联网搜索',\n\t\t\t\t\t\t\t\tvalue: 'web_search'\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t],\n\t\t\t\t\t\tselectedCapabilities: ['deep_thinking', 'web_search'],\n\t\t\t\t\t\tenabled: true,\n\t\t\t\t\t\tstatus: 'idle',\n\t\t\t\t\t\tprogressLogs: [],\n\t\t\t\t\t\tisExpanded: true\n\t\t\t\t\t},\n          {\n            name: 'DeepSeek',\n            avatar: 'https://communication.cn-nb1.rains3.com/Deepseek.png',\n            capabilities: [{\n              label: '深度思考',\n              value: 'deep_thinking'\n            },\n              {\n                label: '联网搜索',\n                value: 'web_search'\n              }\n            ],\n            selectedCapabilities: ['deep_thinking', 'web_search'],\n            enabled: true,\n            status: 'idle',\n            progressLogs: [],\n            isExpanded: true\n          },\n\t\t\t\t\t{\n\t\t\t\t\t\tname: '豆包',\n\t\t\t\t\t\tavatar: 'https://u3w.com/chatfile/%E8%B1%86%E5%8C%85.png',\n\t\t\t\t\t\tcapabilities: [{\n\t\t\t\t\t\t\tlabel: '深度思考',\n\t\t\t\t\t\t\tvalue: 'deep_thinking'\n\t\t\t\t\t\t}],\n\t\t\t\t\t\tselectedCapabilities: ['deep_thinking'],\n\t\t\t\t\t\tenabled: true,\n\t\t\t\t\t\tstatus: 'idle',\n\t\t\t\t\t\tprogressLogs: [],\n\t\t\t\t\t\tisExpanded: true\n\t\t\t\t\t}\n\t\t\t\t],\n\n\t\t\t\t// 输入和任务状态\n\t\t\t\tpromptInput: '',\n\t\t\t\ttaskStarted: false,\n\t\t\t\tenabledAIs: [],\n\n\t\t\t\t// 可视化\n\t\t\t\tscreenshots: [],\n\t\t\t\tautoPlay: false,\n\n\t\t\t\t// 结果\n\t\t\t\tresults: [],\n\t\t\t\tactiveResultIndex: 0,\n\n\t\t\t\t// 历史记录\n\t\t\t\tchatHistory: [],\n\n\t\t\t\t// 评分\n\t\t\t\tselectedResults: [],\n\t\t\t\tscorePrompt: '请你深度阅读以下几篇公众号文章，从多个维度进行逐项打分，输出评分结果。并在以下各篇文章的基础上博采众长，综合整理一篇更全面的文章。',\n\n\t\t\t\t// 收录计数器\n\t\t\t\tcollectNum: 0,\n\n\t\t\t\t// 智能排版\n\t\t\t\tlayoutPrompt: '',\n\n\t\t\t\t// WebSocket\n\t\t\t\tsocketTask: null,\n\t\t\t\treconnectTimer: null,\n\t\t\t\theartbeatTimer: null,\n\t\t\t\treconnectCount: 0,\n\t\t\t\tmaxReconnectCount: 5,\n\t\t\t\tisConnecting: false,\n\t\t\t\tscrollIntoView: '',\n\n\t\t\t\t// 弹窗状态\n\t\t\t\thistoryDrawerVisible: false,\n\t\t\t\tscoreModalVisible: false,\n\t\t\t\tlayoutModalVisible: false,\n\t\t\t\tcurrentLayoutResult: null, // 当前要排版的结果\n\n\t\t\t\t// AI登录状态\n\t\t\t\taiLoginStatus: {\n\t\t\t\t\tyuanbao: false,\n\t\t\t\t\tdoubao: false,\n\t\t\t\t\tagent: false,\n          deepseek: false // DeepSeek初始为未登录状态\n\t\t\t\t},\n\t\t\t\taccounts: {\n\t\t\t\t\tyuanbao: '',\n\t\t\t\t\tdoubao: '',\n\t\t\t\t\tagent: '',\n          deepseek: ''\n\t\t\t\t},\n\t\t\t\tisLoading: {\n\t\t\t\t\tyuanbao: true,\n\t\t\t\t\tdoubao: true,\n\t\t\t\t\tagent: true,\n          deepseek: true // DeepSeek初始为加载状态\n\t\t\t\t}\n\t\t\t};\n\t\t},\n\n\t\tcomputed: {\n\t\t\tcanSend() {\n\t\t\t\t// 检查是否有输入内容\n\t\t\t\tconst hasInput = this.promptInput.trim().length > 0;\n\n\t\t\t\t// 检查是否有可用的AI（既启用又已登录）\n\t\t\t\tconst hasAvailableAI = this.aiList.some(ai => ai.enabled && this.isAiLoginEnabled(ai));\n\n\t\t\t\t// 检查是否正在加载AI状态（如果正在加载，禁用发送按钮）\n\t\t\t\tconst isCheckingStatus = this.isLoading.yuanbao || this.isLoading.doubao || this.isLoading.agent || this.isLoading.deepseek;\n\n\t\t\t\treturn hasInput && hasAvailableAI && !isCheckingStatus;\n\t\t\t},\n\n\t\t\tcanScore() {\n\t\t\t\tconst hasSelected = this.selectedResults.length > 0;\n\t\t\t\tconst hasPrompt = this.scorePrompt.trim().length > 0;\n\t\t\t\tconsole.log('canScore - selectedResults:', this.selectedResults);\n\t\t\t\tconsole.log('canScore - scorePrompt length:', this.scorePrompt.trim().length);\n\t\t\t\tconsole.log('canScore - hasSelected:', hasSelected, 'hasPrompt:', hasPrompt);\n\t\t\t\treturn hasSelected && hasPrompt;\n\t\t\t},\n\n\t\t\tcurrentResult() {\n\t\t\t\treturn this.results[this.activeResultIndex] || null;\n\t\t\t},\n\n\t\t\tgroupedHistory() {\n\t\t\t\tconst groups = {};\n\t\t\t\tconst chatGroups = {};\n\n\t\t\t\t// 首先按chatId分组\n\t\t\t\tthis.chatHistory.forEach(item => {\n\t\t\t\t\tif (!chatGroups[item.chatId]) {\n\t\t\t\t\t\tchatGroups[item.chatId] = [];\n\t\t\t\t\t}\n\t\t\t\t\tchatGroups[item.chatId].push(item);\n\t\t\t\t});\n\n\t\t\t\t// 然后按日期分组，并处理父子关系\n\t\t\t\tObject.values(chatGroups).forEach(chatGroup => {\n\t\t\t\t\t// 按时间排序\n\t\t\t\t\tchatGroup.sort((a, b) => new Date(a.createTime) - new Date(b.createTime));\n\n\t\t\t\t\t// 获取最早的记录作为父级\n\t\t\t\t\tconst parentItem = chatGroup[0];\n\t\t\t\t\tconst date = this.getHistoryDate(parentItem.createTime);\n\n\t\t\t\t\tif (!groups[date]) {\n\t\t\t\t\t\tgroups[date] = [];\n\t\t\t\t\t}\n\n\t\t\t\t\t// 添加父级记录\n\t\t\t\t\tgroups[date].push({\n\t\t\t\t\t\t...parentItem,\n\t\t\t\t\t\tisParent: true,\n\t\t\t\t\t\tisExpanded: this.expandedHistoryItems[parentItem.chatId] || false,\n\t\t\t\t\t\tchildren: chatGroup.slice(1).map(child => ({\n\t\t\t\t\t\t\t...child,\n\t\t\t\t\t\t\tisParent: false\n\t\t\t\t\t\t}))\n\t\t\t\t\t});\n\t\t\t\t});\n\n\t\t\t\treturn groups;\n\t\t\t}\n\t\t},\n\t\tonLoad() {\n\t\t\tthis.initUserInfo();\n\n\t\t\t// 检查用户信息是否完整\n\t\t\tif (!this.userId || !this.corpId) {\n\t\t\t\tconsole.log('用户信息不完整，跳转到登录页面');\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '提示',\n\t\t\t\t\tcontent: '请先登录后再使用',\n\t\t\t\t\tshowCancel: false,\n\t\t\t\t\tconfirmText: '去登录',\n\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\turl: '/pages/login/index'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tthis.initWebSocket();\n\t\t\tthis.loadChatHistory(0); // 加载历史记录\n\t\t\tthis.loadLastChat(); // 加载上次会话\n\t\t\tthis.checkAiLoginStatus(); // 检查AI登录状态\n\t\t},\n\n\t\tonUnload() {\n\t\t\tthis.closeWebSocket();\n\t\t},\n\n\t\tmethods: {\n\t\t\t// 初始化用户信息\n\t\t\tinitUserInfo() {\n\t\t\t\t// 从store获取用户信息，兼容缓存方式\n\t\t\tthis.userId = storage.get(constant.userId);\n\t\t\tthis.corpId = storage.get(constant.corpId);\n\n\t\t\t\tthis.chatId = this.generateUUID();\n\n\t\t\t\t// 初始化请求参数\n\t\t\t\tthis.userInfoReq.userId = this.userId;\n\t\t\t\tthis.userInfoReq.corpId = this.corpId;\n\n\t\t\t\tconsole.log('初始化用户信息:', {\n\t\t\t\t\tuserId: this.userId,\n\t\t\t\t\tcorpId: this.corpId\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// 生成UUID\n\t\t\tgenerateUUID() {\n\t\t\t\treturn uuidv4();\n\t\t\t},\n\n\t\t\t// 切换区域展开状态\n\t\t\ttoggleSection(section) {\n\t\t\t\tthis.sectionExpanded[section] = !this.sectionExpanded[section];\n\t\t\t},\n\n\t\t\t// 切换AI启用状态\n\t\t\ttoggleAI(ai, event) {\n\t\t\t\t// 检查AI是否已登录\n\t\t\t\tif (!this.isAiLoginEnabled(ai)) {\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\tcontent: `${ai.name}需要先登录，请前往PC端进行登录后再使用`,\n\t\t\t\t\t\tshowCancel: false,\n\t\t\t\t\t\tconfirmText: '知道了'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tai.enabled = event.detail.value;\n\t\t\t},\n\n\t\t\t// 切换AI能力\n\t\t\ttoggleCapability(ai, capabilityValue) {\n\t\t\t\t// 检查AI是否已登录和启用\n\t\t\t\tif (!this.isAiLoginEnabled(ai)) {\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\tcontent: `${ai.name}需要先登录，请前往PC端进行登录后再使用`,\n\t\t\t\t\t\tshowCancel: false,\n\t\t\t\t\t\tconfirmText: '知道了'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tif (!ai.enabled) return;\n\n\t\t\t\tconst index = ai.selectedCapabilities.indexOf(capabilityValue);\n\t\t\t\tif (index === -1) {\n\t\t\t\t\tai.selectedCapabilities.push(capabilityValue);\n\t\t\t\t} else {\n\t\t\t\t\tai.selectedCapabilities.splice(index, 1);\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 发送提示词\n\t\t\tsendPrompt() {\n\t\t\t\tif (!this.canSend) return;\n\n\t\t\t\tthis.screenshots = [];\n\t\t\t\t// 折叠所有区域\n\t\t\t\tthis.sectionExpanded.aiConfig = false;\n\t\t\t\tthis.sectionExpanded.promptInput = false;\n\t\t\t\t// this.sectionExpanded.taskStatus = false;\n\n\t\t\t\tthis.taskStarted = true;\n\t\t\t\tthis.results = []; // 清空之前的结果\n\n\t\t\t\tthis.userInfoReq.roles = '';\n\t\t\t\tthis.userInfoReq.taskId = this.generateUUID();\n\t\t\t\tthis.userInfoReq.userId = this.userId;\n\t\t\t\tthis.userInfoReq.corpId = this.corpId;\n\t\t\t\tthis.userInfoReq.userPrompt = this.promptInput;\n\n\t\t\t\t// 获取启用的AI列表及其状态\n\t\t\t\tthis.enabledAIs = this.aiList.filter(ai => ai.enabled);\n\n\t\t\t\t// 将所有启用的AI状态设置为运行中\n\t\t\t\tthis.enabledAIs.forEach(ai => {\n\t\t\t\t\tai.status = 'running';\n\t\t\t\t});\n\n\t\t\t\t// 构建角色参数\n\t\t\t\tthis.enabledAIs.forEach(ai => {\n\t\t\t\t\tif (ai.name === '腾讯元宝T1') {\n\t\t\t\t\t\tthis.userInfoReq.roles = this.userInfoReq.roles + 'yb-hunyuan-pt,';\n\t\t\t\t\t\tif (ai.selectedCapabilities.includes(\"deep_thinking\")) {\n\t\t\t\t\t\t\tthis.userInfoReq.roles = this.userInfoReq.roles + 'yb-hunyuan-sdsk,';\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (ai.selectedCapabilities.includes(\"web_search\")) {\n\t\t\t\t\t\t\tthis.userInfoReq.roles = this.userInfoReq.roles + 'yb-hunyuan-lwss,';\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tif (ai.name === '腾讯元宝DS') {\n\t\t\t\t\t\tthis.userInfoReq.roles = this.userInfoReq.roles + 'yb-deepseek-pt,';\n\t\t\t\t\t\tif (ai.selectedCapabilities.includes(\"deep_thinking\")) {\n\t\t\t\t\t\t\tthis.userInfoReq.roles = this.userInfoReq.roles + 'yb-deepseek-sdsk,';\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (ai.selectedCapabilities.includes(\"web_search\")) {\n\t\t\t\t\t\t\tthis.userInfoReq.roles = this.userInfoReq.roles + 'yb-deepseek-lwss,';\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tif (ai.name === 'AI搜索@元器') {\n\t\t\t\t\t\tthis.userInfoReq.roles = this.userInfoReq.roles + 'cube-trubos-agent,';\n\t\t\t\t\t}\n\t\t\t\t\tif (ai.name === '数智化助手@元器') {\n\t\t\t\t\t\tthis.userInfoReq.roles = this.userInfoReq.roles + 'cube-turbos-large-agent,';\n\t\t\t\t\t}\n          if (ai.name === 'DeepSeek') {\n            this.userInfoReq.roles = this.userInfoReq.roles + 'deepseek,';\n            if (ai.selectedCapabilities.includes(\"deep_thinking\")) {\n              this.userInfoReq.roles = this.userInfoReq.roles + 'ds-sdsk,';\n            }\n            if (ai.selectedCapabilities.includes(\"web_search\")) {\n              this.userInfoReq.roles = this.userInfoReq.roles + 'ds-lwss,';\n            }\n          }\n\t\t\t\t\tif (ai.name === '豆包') {\n\t\t\t\t\t\tthis.userInfoReq.roles = this.userInfoReq.roles + 'zj-db,';\n\t\t\t\t\t\tif (ai.selectedCapabilities.includes(\"deep_thinking\")) {\n\t\t\t\t\t\t\tthis.userInfoReq.roles = this.userInfoReq.roles + 'zj-db-sdsk,';\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\n\t\t\t\tconsole.log(\"参数：\", this.userInfoReq);\n\n\t\t\t\t// 滚动到任务状态区域\n\t\t\t\tthis.scrollIntoView = 'task-status';\n\n\t\t\t\t//调用后端接口\n\t\t\t\tthis.jsonRpcReqest.id = this.generateUUID();\n\t\t\t\tthis.jsonRpcReqest.method = \"使用F8S\";\n\t\t\t\tthis.jsonRpcReqest.params = this.userInfoReq;\n\t\t\t\tthis.message(this.jsonRpcReqest);\n\t\t\t\tthis.userInfoReq.isNewChat = false;\n\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '任务已提交',\n\t\t\t\t\ticon: 'success'\n\t\t\t\t});\n\t\t\t},\n\n\n\n\t\t\t\t\t// WebSocket相关方法\n\t\tinitWebSocket() {\n\t\t\t// 检查用户信息是否完整\n\t\t\tif (!this.userId || !this.corpId) {\n\t\t\t\tconsole.log('用户信息不完整，跳转到登录页面');\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '提示',\n\t\t\t\t\tcontent: '请先登录后再使用',\n\t\t\t\t\tshowCancel: false,\n\t\t\t\t\tconfirmText: '去登录',\n\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\turl: '/pages/login/index'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tif (this.isConnecting) {\n\t\t\t\tconsole.log('WebSocket正在连接中，跳过重复连接');\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tthis.isConnecting = true;\n\n\t\t\t// 使用PC端的WebSocket连接方式\n\t\t\tconst wsUrl = `${process.env.VUE_APP_WS_API || 'wss://u3w.com/cubeServer/websocket?clientId='}mypc-${this.userId}`;\n\t\t\t// const wsUrl = `${process.env.VUE_APP_WS_API || 'ws://127.0.0.1:8081/websocket?clientId='}mypc-${this.userId}`;\n\t\t\tconsole.log('WebSocket URL:', wsUrl);\n\n\t\t\tthis.socketTask = uni.connectSocket({\n\t\t\t\turl: wsUrl,\n\t\t\t\tsuccess: () => {\n\t\t\t\t\tconsole.log('WebSocket连接成功');\n\t\t\t\t},\n\t\t\t\tfail: (err) => {\n\t\t\t\t\tconsole.error('WebSocket连接失败', err);\n\t\t\t\t\tthis.isConnecting = false;\n\t\t\t\t\tthis.handleReconnect();\n\t\t\t\t}\n\t\t\t});\n\n\t\t\tthis.socketTask.onOpen(() => {\n\t\t\t\tconsole.log('WebSocket连接已打开');\n\t\t\t\tthis.isConnecting = false;\n\t\t\t\tthis.reconnectCount = 0; // 重置重连次数\n\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '连接成功',\n\t\t\t\t\ticon: 'success',\n\t\t\t\t\tduration: 1000\n\t\t\t\t});\n\n\t\t\t\t// 开始心跳检测\n\t\t\t\tthis.startHeartbeat();\n\t\t\t});\n\n\t\t\tthis.socketTask.onMessage((res) => {\n\t\t\t\tthis.handleWebSocketMessage(res.data);\n\t\t\t});\n\n\t\t\tthis.socketTask.onError((err) => {\n\t\t\t\tconsole.error('WebSocket连接错误', err);\n\t\t\t\tthis.isConnecting = false;\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: 'WebSocket连接错误',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\tthis.handleReconnect();\n\t\t\t});\n\n\t\t\tthis.socketTask.onClose(() => {\n\t\t\t\tconsole.log('WebSocket连接已关闭');\n\t\t\t\tthis.isConnecting = false;\n\t\t\t\tthis.stopHeartbeat(); // 停止心跳\n\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: 'WebSocket连接已关闭',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\n\t\t\t\t// 尝试重连\n\t\t\t\tthis.handleReconnect();\n\t\t\t});\n\t\t},\n\n\t\t// 处理重连\n\t\thandleReconnect() {\n\t\t\tif (this.reconnectCount >= this.maxReconnectCount) {\n\t\t\t\tconsole.log('WebSocket重连次数已达上限');\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '连接失败',\n\t\t\t\t\tcontent: '网络连接不稳定，请检查网络后手动刷新页面',\n\t\t\t\t\tshowCancel: false,\n\t\t\t\t\tconfirmText: '知道了'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tthis.reconnectCount++;\n\t\t\tconst delay = Math.min(1000 * Math.pow(2, this.reconnectCount), 30000); // 指数退避，最大30秒\n\n\t\t\tconsole.log(`WebSocket将在${delay}ms后进行第${this.reconnectCount}次重连`);\n\n\t\t\tthis.reconnectTimer = setTimeout(() => {\n\t\t\t\tconsole.log(`开始第${this.reconnectCount}次重连`);\n\t\t\t\tthis.initWebSocket();\n\t\t\t}, delay);\n\t\t},\n\n\t\t// 开始心跳检测\n\t\tstartHeartbeat() {\n\t\t\tthis.stopHeartbeat(); // 先停止之前的心跳\n\n\t\t\tthis.heartbeatTimer = setInterval(() => {\n\t\t\t\tif (this.socketTask) {\n\t\t\t\t\tthis.sendWebSocketMessage({\n\t\t\t\t\t\ttype: 'HEARTBEAT',\n\t\t\t\t\t\ttimestamp: Date.now()\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}, 30000); // 每30秒发送一次心跳\n\t\t},\n\n\t\t// 停止心跳检测\n\t\tstopHeartbeat() {\n\t\t\tif (this.heartbeatTimer) {\n\t\t\t\tclearInterval(this.heartbeatTimer);\n\t\t\t\tthis.heartbeatTimer = null;\n\t\t\t}\n\t\t},\n\n\t\t\tsendWebSocketMessage(data) {\n\t\t\t\tif (this.socketTask) {\n\t\t\t\t\tthis.socketTask.send({\n\t\t\t\t\t\tdata: JSON.stringify(data)\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tconsole.warn('WebSocket未连接，无法发送消息');\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 调用后端message接口\n\t\t\tmessage(data) {\n\t\t\t\tmessage(data).then(res => {\n\t\t\t\t\tif (res.code == 201) {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: res.messages,\n\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\tduration: 1500,\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t\t\tcloseWebSocket() {\n\t\t\t// 清理重连定时器\n\t\t\tif (this.reconnectTimer) {\n\t\t\t\tclearTimeout(this.reconnectTimer);\n\t\t\t\tthis.reconnectTimer = null;\n\t\t\t}\n\n\t\t\t// 停止心跳检测\n\t\t\tthis.stopHeartbeat();\n\n\t\t\t// 关闭WebSocket连接\n\t\t\tif (this.socketTask) {\n\t\t\t\tthis.socketTask.close();\n\t\t\t\tthis.socketTask = null;\n\t\t\t}\n\n\t\t\t// 重置状态\n\t\t\tthis.isConnecting = false;\n\t\t\tthis.reconnectCount = 0;\n\t\t},\n\n\t\t\t\t\t// 处理WebSocket消息\n\t\thandleWebSocketMessage(data) {\n\t\t\ttry {\n\t\t\t\tconst datastr = data;\n\t\t\t\tconst dataObj = JSON.parse(datastr);\n\n\t\t\t\t// 忽略心跳响应\n\t\t\t\tif (dataObj.type === 'HEARTBEAT_RESPONSE' || dataObj.type === 'HEARTBEAT') {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\t\t// 处理chatId消息\n\t\t\t\t\tif (dataObj.type === 'RETURN_YBT1_CHATID' && dataObj.chatId) {\n\t\t\t\t\t\tthis.userInfoReq.toneChatId = dataObj.chatId;\n\t\t\t\t\t} else if (dataObj.type === 'RETURN_YBDS_CHATID' && dataObj.chatId) {\n\t\t\t\t\t\tthis.userInfoReq.ybDsChatId = dataObj.chatId;\n\t\t\t\t\t} else if (dataObj.type === 'RETURN_DB_CHATID' && dataObj.chatId) {\n\t\t\t\t\t\tthis.userInfoReq.dbChatId = dataObj.chatId;\n\t\t\t\t\t}\n\n\t\t\t\t\t// 处理进度日志消息\n\t\t\t\t\tif (dataObj.type === 'RETURN_PC_TASK_LOG' && dataObj.aiName) {\n\t\t\t\t\t\tconst targetAI = this.enabledAIs.find(ai => ai.name === dataObj.aiName);\n\t\t\t\t\t\tif (targetAI) {\n\t\t\t\t\t\t\t// 将新进度添加到数组开头\n\t\t\t\t\t\t\ttargetAI.progressLogs.unshift({\n\t\t\t\t\t\t\t\tcontent: dataObj.content,\n\t\t\t\t\t\t\t\ttimestamp: new Date(),\n\t\t\t\t\t\t\t\tisCompleted: false\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\t// 处理截图消息\n\t\t\t\t\tif (dataObj.type === 'RETURN_PC_TASK_IMG' && dataObj.url) {\n\t\t\t\t\t\t// 将新的截图添加到数组开头\n\t\t\t\t\t\tthis.screenshots.unshift(dataObj.url);\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\t// 处理智能评分结果\n\t\t\t\t\tif (dataObj.type === 'RETURN_WKPF_RES') {\n\t\t\t\t\t\tconst wkpfAI = this.enabledAIs.find(ai => ai.name === '智能评分');\n\t\t\t\t\t\tif (wkpfAI) {\n\t\t\t\t\t\t\twkpfAI.status = 'completed';\n\t\t\t\t\t\t\tif (wkpfAI.progressLogs.length > 0) {\n\t\t\t\t\t\t\t\twkpfAI.progressLogs[0].isCompleted = true;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t// 添加评分结果到results最前面\n\t\t\t\t\t\t\tthis.results.unshift({\n\t\t\t\t\t\t\t\taiName: '智能评分',\n\t\t\t\t\t\t\t\tcontent: dataObj.draftContent,\n\t\t\t\t\t\t\t\tshareUrl: dataObj.shareUrl || '',\n\t\t\t\t\t\t\t\tshareImgUrl: dataObj.shareImgUrl || '',\n\t\t\t\t\t\t\t\ttimestamp: new Date()\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\tthis.activeResultIndex = 0;\n\n\t\t\t\t\t\t\t// 折叠所有区域当智能评分完成时\n\t\t\t\t\t\t\tthis.sectionExpanded.aiConfig = false;\n\t\t\t\t\t\t\tthis.sectionExpanded.promptInput = false;\n\t\t\t\t\t\t\tthis.sectionExpanded.taskStatus = false;\n\n\t\t\t\t\t\t\t// 智能评分完成时，再次保存历史记录\n\t\t\t\t\t\t\tthis.saveHistory();\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\t// 处理智能排版结果\n\t\t\t\t\tif (dataObj.type === 'RETURN_ZNPB_RES') {\n\t\t\t\t\t\tconsole.log(\"收到智能排版结果\", dataObj);\n\t\t\t\t\t\tconsole.log(\"当前 currentLayoutResult:\", this.currentLayoutResult);\n\t\t\t\t\t\t\n\t\t\t\t\t\tconst znpbAI = this.enabledAIs.find(ai => ai.name === '智能排版');\n\t\t\t\t\t\tif (znpbAI) {\n\t\t\t\t\t\t\tznpbAI.status = 'completed';\n\t\t\t\t\t\t\tif (znpbAI.progressLogs.length > 0) {\n\t\t\t\t\t\t\t\tznpbAI.progressLogs[0].isCompleted = true;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t// 不添加到结果展示，直接调用推送方法\n\t\t\t\t\t\t\tthis.handlePushToWechat(dataObj.draftContent);\n\n\t\t\t\t\t\t\t// 智能排版完成时，保存历史记录\n\t\t\t\t\t\t\tthis.saveHistory();\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\t// 处理AI登录状态消息\n\t\t\t\t\tthis.handleAiStatusMessage(datastr, dataObj);\n\n\t\t\t\t\t// 处理AI结果\n\t\t\t\t\tthis.handleAIResult(dataObj);\n\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('WebSocket消息处理错误', error);\n\t\t\t\t}\n\t\t\t},\n\n\t\t\thandleAiStatusMessage(datastr, dataObj) {\n\t\t\t\t// 处理腾讯元宝登录状态\n\t\t\t\tif (datastr.includes(\"RETURN_YB_STATUS\") && dataObj.status != '') {\n\t\t\t\t\tthis.isLoading.yuanbao = false;\n\t\t\t\t\tif (!datastr.includes(\"false\")) {\n\t\t\t\t\t\tthis.aiLoginStatus.yuanbao = true;\n\t\t\t\t\t\tthis.accounts.yuanbao = dataObj.status;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.aiLoginStatus.yuanbao = false;\n\t\t\t\t\t\t// 禁用相关AI\n\t\t\t\t\t\tthis.disableAIsByLoginStatus('yuanbao');\n\t\t\t\t\t}\n\t\t\t\t\t// 更新AI启用状态\n\t\t\t\t\tthis.updateAiEnabledStatus();\n\t\t\t\t}\n\t\t\t\t// 处理豆包登录状态\n\t\t\t\telse if (datastr.includes(\"RETURN_DB_STATUS\") && dataObj.status != '') {\n\t\t\t\t\tthis.isLoading.doubao = false;\n\t\t\t\t\tif (!datastr.includes(\"false\")) {\n\t\t\t\t\t\tthis.aiLoginStatus.doubao = true;\n\t\t\t\t\t\tthis.accounts.doubao = dataObj.status;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.aiLoginStatus.doubao = false;\n\t\t\t\t\t\t// 禁用相关AI\n\t\t\t\t\t\tthis.disableAIsByLoginStatus('doubao');\n\t\t\t\t\t}\n\t\t\t\t\t// 更新AI启用状态\n\t\t\t\t\tthis.updateAiEnabledStatus();\n\t\t\t\t}\n        // 处理DeepSeek登录状态\n        else if (datastr.includes(\"RETURN_DEEPSEEK_STATUS\")) {\n          console.log(\"收到DeepSeek登录状态消息:\", dataObj);\n          this.isLoading.deepseek = false;\n          if (dataObj.status && dataObj.status !== 'false' && dataObj.status !== '') {\n            this.aiLoginStatus.deepseek = true;\n            this.accounts.deepseek = dataObj.status;\n            console.log(\"DeepSeek登录成功，账号:\", dataObj.status);\n            \n            // 查找DeepSeek AI实例\n            const deepseekAI = this.aiList.find(ai => ai.name === 'DeepSeek');\n\n          } else {\n            this.aiLoginStatus.deepseek = false;\n            this.accounts.deepseek = '';\n            console.log(\"DeepSeek未登录\");\n            \n            // 如果未登录，确保DeepSeek被禁用\n            const deepseekAI = this.aiList.find(ai => ai.name === 'DeepSeek');\n  \n          }\n          // 强制更新UI\n          this.$forceUpdate();\n        }\n\t\t\t\t// 处理智能体登录状态\n\t\t\t\telse if (datastr.includes(\"RETURN_AGENT_STATUS\") && dataObj.status != '') {\n\t\t\t\t\tthis.isLoading.agent = false;\n\t\t\t\t\tif (!datastr.includes(\"false\")) {\n\t\t\t\t\t\tthis.aiLoginStatus.agent = true;\n\t\t\t\t\t\tthis.accounts.agent = dataObj.status;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.aiLoginStatus.agent = false;\n\t\t\t\t\t\t// 禁用相关AI\n\t\t\t\t\t\tthis.disableAIsByLoginStatus('agent');\n\t\t\t\t\t}\n\t\t\t\t\t// 更新AI启用状态\n\t\t\t\t\tthis.updateAiEnabledStatus();\n\t\t\t\t}\n\t\t\t},\n\n\t\t\thandleAIResult(dataObj) {\n\t\t\t\tlet targetAI = null;\n\n\t\t\t\t// 根据消息类型匹配AI\n\t\t\t\tswitch (dataObj.type) {\n\t\t\t\t\tcase 'RETURN_YBT1_RES':\n\t\t\t\t\t\tconsole.log('收到消息:', dataObj);\n\t\t\t\t\t\ttargetAI = this.enabledAIs.find(ai => ai.name === '腾讯元宝T1');\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 'RETURN_YBDS_RES':\n\t\t\t\t\t\tconsole.log('收到消息:', dataObj);\n\t\t\t\t\t\ttargetAI = this.enabledAIs.find(ai => ai.name === '腾讯元宝DS');\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 'RETURN_DB_RES':\n\t\t\t\t\t\tconsole.log('收到消息:', dataObj);\n\t\t\t\t\t\ttargetAI = this.enabledAIs.find(ai => ai.name === '豆包');\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 'RETURN_TURBOS_RES':\n\t\t\t\t\t\tconsole.log('收到消息:', dataObj);\n\t\t\t\t\t\ttargetAI = this.enabledAIs.find(ai => ai.name === 'AI搜索@元器');\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 'RETURN_TURBOS_LARGE_RES':\n\t\t\t\t\t\tconsole.log('收到消息:', dataObj);\n\t\t\t\t\t\ttargetAI = this.enabledAIs.find(ai => ai.name === '数智化助手@元器');\n\t\t\t\t\t\tbreak;\n          case 'RETURN_DEEPSEEK_RES':\n            console.log('收到DeepSeek消息:', dataObj);\n            targetAI = this.enabledAIs.find(ai => ai.name === 'DeepSeek');\n            // 如果找不到DeepSeek，可能是因为它不在enabledAIs中，尝试添加它\n            if (!targetAI) {\n              targetAI = {\n                name: 'DeepSeek',\n                avatar: 'https://communication.cn-nb1.rains3.com/Deepseek.png',\n                capabilities: [{\n                  label: '深度思考',\n                  value: 'deep_thinking'\n                },\n                  {\n                    label: '联网搜索',\n                    value: 'web_search'\n                  }],\n                selectedCapabilities: ['deep_thinking', 'web_search'],\n                enabled: true,\n                status: 'running',\n                progressLogs: [{\n                  content: 'DeepSeek响应已接收',\n                  timestamp: new Date(),\n                  isCompleted: true\n                }],\n                isExpanded: true\n              };\n              this.enabledAIs.push(targetAI);\n            }\n            break;\n\t\t\t\t}\n\n\t\t\t\tif (targetAI) {\n\t\t\t\t\t// 更新AI状态为已完成\n\t\t\t\t\ttargetAI.status = 'completed';\n\n\t\t\t\t\t// 将最后一条进度消息标记为已完成\n\t\t\t\t\tif (targetAI.progressLogs.length > 0) {\n\t\t\t\t\t\ttargetAI.progressLogs[0].isCompleted = true;\n\t\t\t\t\t}\n\n\t\t\t\t\t// 添加结果到数组开头\n\t\t\t\t\tconst resultIndex = this.results.findIndex(r => r.aiName === targetAI.name);\n\t\t\t\t\tif (resultIndex === -1) {\n\t\t\t\t\t\tthis.results.unshift({\n\t\t\t\t\t\t\taiName: targetAI.name,\n\t\t\t\t\t\t\tcontent: dataObj.draftContent,\n\t\t\t\t\t\t\tshareUrl: dataObj.shareUrl || '',\n\t\t\t\t\t\t\tshareImgUrl: dataObj.shareImgUrl || '',\n\t\t\t\t\t\t\ttimestamp: new Date()\n\t\t\t\t\t\t});\n\t\t\t\t\t\tthis.activeResultIndex = 0;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.results.splice(resultIndex, 1);\n\t\t\t\t\t\tthis.results.unshift({\n\t\t\t\t\t\t\taiName: targetAI.name,\n\t\t\t\t\t\t\tcontent: dataObj.draftContent,\n\t\t\t\t\t\t\tshareUrl: dataObj.shareUrl || '',\n\t\t\t\t\t\t\tshareImgUrl: dataObj.shareImgUrl || '',\n\t\t\t\t\t\t\ttimestamp: new Date()\n\t\t\t\t\t\t});\n\t\t\t\t\t\tthis.activeResultIndex = 0;\n\t\t\t\t\t}\n\n\t\t\t\t\t// 折叠所有区域当有结果返回时\n\t\t\t\t\tthis.sectionExpanded.aiConfig = false;\n\t\t\t\t\tthis.sectionExpanded.promptInput = false;\n\t\t\t\t\tthis.sectionExpanded.taskStatus = false;\n\n\t\t\t\t\t// 滚动到结果区域\n\t\t\t\t\tthis.scrollIntoView = 'results';\n\n\t\t\t\t\t// 保存历史记录\n\t\t\t\t\tthis.saveHistory();\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 状态相关方法\n\t\t\tgetStatusText(status) {\n\t\t\t\tconst statusMap = {\n\t\t\t\t\t'idle': '等待中',\n\t\t\t\t\t'running': '正在执行',\n\t\t\t\t\t'completed': '已完成',\n\t\t\t\t\t'failed': '执行失败'\n\t\t\t\t};\n\t\t\t\treturn statusMap[status] || '未知状态';\n\t\t\t},\n\n\t\t\tgetStatusIconClass(status) {\n\t\t\t\tconst classMap = {\n\t\t\t\t\t'idle': 'status-idle',\n\t\t\t\t\t'running': 'status-running',\n\t\t\t\t\t'completed': 'status-completed',\n\t\t\t\t\t'failed': 'status-failed'\n\t\t\t\t};\n\t\t\t\treturn classMap[status] || 'status-unknown';\n\t\t\t},\n\n\t\t\tgetStatusEmoji(status) {\n\t\t\t\tconst emojiMap = {\n\t\t\t\t\t'idle': '⏳',\n\t\t\t\t\t'running': '🔄',\n\t\t\t\t\t'completed': '✅',\n\t\t\t\t\t'failed': '❌'\n\t\t\t\t};\n\t\t\t\treturn emojiMap[status] || '❓';\n\t\t\t},\n\n\t\t\t// 切换任务展开状态\n\t\t\ttoggleTaskExpansion(ai) {\n\t\t\t\tai.isExpanded = !ai.isExpanded;\n\t\t\t},\n\n\t\t\t// 切换自动播放\n\t\t\ttoggleAutoPlay(event) {\n\t\t\t\tthis.autoPlay = event.detail.value;\n\t\t\t},\n\n\t\t\t// 预览图片\n\t\t\tpreviewImage(url) {\n\t\t\t\tuni.previewImage({\n\t\t\t\t\tcurrent: url,\n\t\t\t\t\turls: [url]\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// 结果相关方法\n\t\t\tswitchResultTab(index) {\n\t\t\t\tthis.activeResultIndex = index;\n\t\t\t},\n\n\t\t\trenderMarkdown(text) {\n\t\t\t\ttry {\n          // 对于DeepSeek响应，添加特殊的CSS类\n          if (this.currentResult && this.currentResult.aiName === 'DeepSeek') {\n            // 检查是否已经包含了deepseek-response类\n            if (text && text.includes('class=\"deepseek-response\"')) {\n              return text; // 已经包含了特殊类，直接返回\n            }\n            const renderedHtml = marked(text);\n            return `<div class=\"deepseek-response\">${renderedHtml}</div>`;\n          }\n\t\t\t\t\treturn marked(text);\n\t\t\t\t} catch (error) {\n\t\t\t\t\treturn text;\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tisImageFile(url) {\n\t\t\t\tif (!url) return false;\n\t\t\t\tconst imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'];\n\t\t\t\tconst urlLower = url.toLowerCase();\n\t\t\t\treturn imageExtensions.some(ext => urlLower.includes(ext));\n\t\t\t},\n\n\t\t\t// 判断是否为PDF文件\n\t\t\tisPdfFile(url) {\n\t\t\t\tif (!url) return false;\n\t\t\t\treturn url.toLowerCase().includes('.pdf');\n\t\t\t},\n\n\t\t\tcopyResult(content) {\n\t\t\t\tuni.setClipboardData({\n\t\t\t\t\tdata: content,\n\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '已复制到剪贴板',\n\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\n\n\n\t\t\t// shareResult(result) {\n\t\t\t// \tuni.share({\n\t\t\t// \t\tprovider: 'weixin',\n\t\t\t// \t\tscene: 'WXSceneSession',\n\t\t\t// \t\ttype: 0,\n\t\t\t// \t\ttitle: `${result.aiName}的执行结果`,\n\t\t\t// \t\tsummary: result.content.substring(0, 100),\n\t\t\t// \t\tsuccess: () => {\n\t\t\t// \t\t\tuni.showToast({\n\t\t\t// \t\t\t\ttitle: '分享成功',\n\t\t\t// \t\t\t\ticon: 'success'\n\t\t\t// \t\t\t});\n\t\t\t// \t\t}\n\t\t\t// \t});\n\t\t\t// },\n\n\t\t\texportResult(result) {\n\t\t\t\t// 小程序环境下的导出功能可以通过分享或复制实现\n\t\t\t\tthis.copyResult(result.content);\n\t\t\t},\n\n\t\t\topenShareUrl(url) {\n\t\t\t\tuni.setClipboardData({\n\t\t\t\t\tdata: url,\n\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '原链接已复制',\n\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t});\n\t\t\t\t\t},\n\t\t\t\t\tfail: () => {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '复制失败',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// 复制PDF链接\n\t\t\tcopyPdfUrl(url) {\n\t\t\t\tuni.setClipboardData({\n\t\t\t\t\tdata: url,\n\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: 'PDF链接已复制',\n\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t});\n\t\t\t\t\t},\n\t\t\t\t\tfail: () => {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '复制失败',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// 打开PDF文件\n\t\t\topenPdfFile(url) {\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '正在下载PDF...'\n\t\t\t\t});\n\n\t\t\t\t// 尝试下载并打开文件\n\t\t\t\tuni.downloadFile({\n\t\t\t\t\turl: url,\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\tif (res.statusCode === 200) {\n\t\t\t\t\t\t\t// 打开文件\n\t\t\t\t\t\t\tuni.openDocument({\n\t\t\t\t\t\t\t\tfilePath: res.tempFilePath,\n\t\t\t\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\ttitle: 'PDF已打开',\n\t\t\t\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\tfail: () => {\n\t\t\t\t\t\t\t\t\t// 如果无法打开，提示并复制链接\n\t\t\t\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\t\t\t\t\tcontent: '无法在当前环境打开PDF文件，已复制链接到剪贴板，请在浏览器中打开',\n\t\t\t\t\t\t\t\t\t\tshowCancel: false,\n\t\t\t\t\t\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\t\t\t\t\t\tuni.setClipboardData({\n\t\t\t\t\t\t\t\t\t\t\t\tdata: url\n\t\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '下载失败',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t},\n\t\t\t\t\tfail: () => {\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t// 下载失败，提示并复制链接\n\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\t\tcontent: '下载失败，已复制PDF链接到剪贴板，请在浏览器中打开',\n\t\t\t\t\t\t\tshowCancel: false,\n\t\t\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\t\t\tuni.setClipboardData({\n\t\t\t\t\t\t\t\t\tdata: url\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// 历史记录相关方法\n\t\t\tshowHistoryDrawer() {\n\t\t\t\tthis.historyDrawerVisible = true;\n\t\t\t\tthis.loadChatHistory(1);\n\t\t\t},\n\n\t\t\tcloseHistoryDrawer() {\n\t\t\t\tthis.historyDrawerVisible = false;\n\t\t\t},\n\n\t\t\tasync loadChatHistory(isAll) {\n\t\t\t\ttry {\n\t\t\t\t\tconst res = await getChatHistory(this.userId, isAll);\n\t\t\t\t\tif (res.code === 200) {\n\t\t\t\t\t\tthis.chatHistory = res.data || [];\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('加载历史记录失败:', error);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '加载历史记录失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tloadHistoryItem(item) {\n\t\t\t\ttry {\n\t\t\t\t\tconst historyData = JSON.parse(item.data);\n\t\t\t\t\t// 恢复AI选择配置\n\t\t\t\t\tthis.aiList = historyData.aiList || this.aiList;\n\t\t\t\t\t// 恢复提示词输入\n\t\t\t\t\tthis.promptInput = historyData.promptInput || item.userPrompt;\n\t\t\t\t\t// 恢复任务流程\n\t\t\t\t\tthis.enabledAIs = historyData.enabledAIs || [];\n\t\t\t\t\t// 恢复主机可视化\n\t\t\t\t\tthis.screenshots = historyData.screenshots || [];\n\t\t\t\t\t// 恢复执行结果\n\t\t\t\t\tthis.results = historyData.results || [];\n\t\t\t\t\t// 恢复chatId\n\t\t\t\t\tthis.chatId = item.chatId || this.chatId;\n\t\t\t\t\tthis.userInfoReq.toneChatId = item.toneChatId || '';\n\t\t\t\t\tthis.userInfoReq.ybDsChatId = item.ybDsChatId || '';\n\t\t\t\t\tthis.userInfoReq.dbChatId = item.dbChatId || '';\n\t\t\t\t\tthis.userInfoReq.isNewChat = false;\n\n\t\t\t\t\t// 不再根据AI登录状态更新AI启用状态，保持原有选择\n\n\t\t\t\t\t// 展开相关区域\n\t\t\t\t\tthis.sectionExpanded.aiConfig = true;\n\t\t\t\t\tthis.sectionExpanded.promptInput = true;\n\t\t\t\t\tthis.sectionExpanded.taskStatus = true;\n\t\t\t\t\tthis.taskStarted = true;\n\n\t\t\t\t\tthis.closeHistoryDrawer();\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '历史记录加载成功',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t});\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('加载历史记录失败:', error);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '加载失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 加载上次会话\n\t\t\tasync loadLastChat() {\n\t\t\t\ttry {\n\t\t\t\t\tconst res = await getChatHistory(this.userId, 0);\n\t\t\t\t\tif (res.code === 200 && res.data && res.data.length > 0) {\n\t\t\t\t\t\t// 获取最新的会话记录\n\t\t\t\t\t\tconst lastChat = res.data[0];\n\t\t\t\t\t\tthis.loadHistoryItem(lastChat);\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('加载上次会话失败:', error);\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tasync saveHistory() {\n\t\t\t\tconst historyData = {\n\t\t\t\t\taiList: this.aiList,\n\t\t\t\t\tpromptInput: this.promptInput,\n\t\t\t\t\tenabledAIs: this.enabledAIs,\n\t\t\t\t\tscreenshots: this.screenshots,\n\t\t\t\t\tresults: this.results,\n\t\t\t\t\tchatId: this.chatId,\n\t\t\t\t\ttoneChatId: this.userInfoReq.toneChatId,\n\t\t\t\t\tybDsChatId: this.userInfoReq.ybDsChatId,\n\t\t\t\t\tdbChatId: this.userInfoReq.dbChatId\n\t\t\t\t};\n\n\t\t\t\ttry {\n\t\t\t\t\tawait saveUserChatData({\n\t\t\t\t\t\tuserId: this.userId,\n\t\t\t\t\t\tuserPrompt: this.promptInput,\n\t\t\t\t\t\tdata: JSON.stringify(historyData),\n\t\t\t\t\t\tchatId: this.chatId,\n\t\t\t\t\t\ttoneChatId: this.userInfoReq.toneChatId,\n\t\t\t\t\t\tybDsChatId: this.userInfoReq.ybDsChatId,\n\t\t\t\t\t\tdbChatId: this.userInfoReq.dbChatId\n\t\t\t\t\t});\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('保存历史记录失败:', error);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '保存历史记录失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tgetHistoryDate(timestamp) {\n\t\t\t\ttry {\n\t\t\t\t\tconsole.log('getHistoryDate 输入:', timestamp, typeof timestamp);\n\n\t\t\t\t\tif (!timestamp) {\n\t\t\t\t\t\treturn '未知日期';\n\t\t\t\t\t}\n\n\t\t\t\t\tlet date;\n\n\t\t\t\t\tif (typeof timestamp === 'number') {\n\t\t\t\t\t\tdate = new Date(timestamp);\n\t\t\t\t\t} else if (typeof timestamp === 'string') {\n\t\t\t\t\t\t// 处理 \"2025-6-23 14:53:12\" 这种格式\n\t\t\t\t\t\tconst match = timestamp.match(/(\\d{4})-(\\d{1,2})-(\\d{1,2})\\s+(\\d{1,2}):(\\d{1,2}):(\\d{1,2})/);\n\t\t\t\t\t\tif (match) {\n\t\t\t\t\t\t\tconst [, year, month, day, hour, minute, second] = match;\n\t\t\t\t\t\t\tdate = new Date(\n\t\t\t\t\t\t\t\tparseInt(year),\n\t\t\t\t\t\t\t\tparseInt(month) - 1,\n\t\t\t\t\t\t\t\tparseInt(day),\n\t\t\t\t\t\t\t\tparseInt(hour),\n\t\t\t\t\t\t\t\tparseInt(minute),\n\t\t\t\t\t\t\t\tparseInt(second)\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// 如果正则不匹配，尝试其他方式\n\t\t\t\t\t\t\tconst fixedTimestamp = timestamp.replace(/\\s/g, 'T');\n\t\t\t\t\t\t\tdate = new Date(fixedTimestamp);\n\n\t\t\t\t\t\t\tif (isNaN(date.getTime())) {\n\t\t\t\t\t\t\t\tdate = new Date(timestamp);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tdate = new Date(timestamp);\n\t\t\t\t\t}\n\n\t\t\t\t\tconsole.log('getHistoryDate 解析结果:', date, date.getTime());\n\n\t\t\t\t\tif (isNaN(date.getTime())) {\n\t\t\t\t\t\treturn '未知日期';\n\t\t\t\t\t}\n\n\t\t\t\t\tconst today = new Date();\n\t\t\t\t\tconst yesterday = new Date(today);\n\t\t\t\t\tyesterday.setDate(yesterday.getDate() - 1);\n\n\t\t\t\t\tif (date.toDateString() === today.toDateString()) {\n\t\t\t\t\t\treturn '今天';\n\t\t\t\t\t} else if (date.toDateString() === yesterday.toDateString()) {\n\t\t\t\t\t\treturn '昨天';\n\t\t\t\t\t} else {\n\t\t\t\t\t\treturn date.toLocaleDateString('zh-CN');\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('格式化日期错误:', error, timestamp);\n\t\t\t\t\treturn '未知日期';\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 格式化历史记录时间\n\t\t\tformatHistoryTime(timestamp) {\n\t\t\t\ttry {\n\t\t\t\t\tconsole.log('formatHistoryTime 输入:', timestamp, typeof timestamp);\n\n\t\t\t\t\tlet date;\n\n\t\t\t\t\tif (!timestamp) {\n\t\t\t\t\t\treturn '时间未知';\n\t\t\t\t\t}\n\n\t\t\t\t\t// 如果是数字，直接创建Date对象\n\t\t\t\t\tif (typeof timestamp === 'number') {\n\t\t\t\t\t\tdate = new Date(timestamp);\n\t\t\t\t\t} else if (typeof timestamp === 'string') {\n\t\t\t\t\t\t// 处理ISO 8601格式：2025-06-25T07:18:54.110Z\n\t\t\t\t\t\tif (timestamp.includes('T') && (timestamp.includes('Z') || timestamp.includes('+'))) {\n\t\t\t\t\t\t\tdate = new Date(timestamp);\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// 处理 \"2025-6-26 08:46:26\" 这种格式\n\t\t\t\t\t\telse {\n\t\t\t\t\t\t\tconst match = timestamp.match(/(\\d{4})-(\\d{1,2})-(\\d{1,2})\\s+(\\d{1,2}):(\\d{1,2}):(\\d{1,2})/);\n\t\t\t\t\t\t\tif (match) {\n\t\t\t\t\t\t\t\tconst [, year, month, day, hour, minute, second] = match;\n\t\t\t\t\t\t\t\t// 注意：Date构造函数的month参数是0-11，所以要减1\n\t\t\t\t\t\t\t\tdate = new Date(\n\t\t\t\t\t\t\t\t\tparseInt(year),\n\t\t\t\t\t\t\t\t\tparseInt(month) - 1,\n\t\t\t\t\t\t\t\t\tparseInt(day),\n\t\t\t\t\t\t\t\t\tparseInt(hour),\n\t\t\t\t\t\t\t\t\tparseInt(minute),\n\t\t\t\t\t\t\t\t\tparseInt(second)\n\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t// 如果正则不匹配，尝试其他方式\n\t\t\t\t\t\t\t\tconst fixedTimestamp = timestamp.replace(/\\s/g, 'T');\n\t\t\t\t\t\t\t\tdate = new Date(fixedTimestamp);\n\n\t\t\t\t\t\t\t\tif (isNaN(date.getTime())) {\n\t\t\t\t\t\t\t\t\tdate = new Date(timestamp);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t} else if (timestamp instanceof Date) {\n\t\t\t\t\t\tdate = timestamp;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tdate = new Date(timestamp);\n\t\t\t\t\t}\n\n\t\t\t\t\tconsole.log('formatHistoryTime 解析结果:', date, date.getTime());\n\n\t\t\t\t\tif (isNaN(date.getTime())) {\n\t\t\t\t\t\treturn '时间未知';\n\t\t\t\t\t}\n\n\t\t\t\t\t// 使用更简洁的时间格式，避免显示时区信息\n\t\t\t\t\tconst hour = date.getHours().toString().padStart(2, '0');\n\t\t\t\t\tconst minute = date.getMinutes().toString().padStart(2, '0');\n\n\t\t\t\t\tconst timeString = `${hour}:${minute}`;\n\n\t\t\t\t\tconsole.log('formatHistoryTime 输出:', timeString);\n\t\t\t\t\treturn timeString;\n\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('格式化时间错误:', error, timestamp);\n\t\t\t\t\treturn '时间未知';\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 修改折叠切换方法\n\t\t\ttoggleHistoryExpansion(item) {\n\t\t\t\tthis.expandedHistoryItems[item.chatId] = !this.expandedHistoryItems[item.chatId];\n\t\t\t\tthis.$forceUpdate(); // 强制更新视图\n\t\t\t},\n\n\t\t\t// 智能评分相关方法\n\t\t\tshowScoreModal() {\n\t\t\t\tthis.selectedResults = [];\n\t\t\t\tthis.scoreModalVisible = true;\n\t\t\t},\n\n\t\t\tcloseScoreModal() {\n\t\t\t\tthis.scoreModalVisible = false;\n\t\t\t},\n\n\t\t\t// 智能排版相关方法\n\t\t\tshowLayoutModal() {\n\t\t\t\tif (!this.currentResult) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '没有可排版的内容',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tconsole.log(\"showLayoutModal\", this.currentResult);\n\t\t\t\t// 深度拷贝当前结果，避免引用被修改\n\t\t\t\tthis.currentLayoutResult = {\n\t\t\t\t\taiName: this.currentResult.aiName,\n\t\t\t\t\tcontent: this.currentResult.content,\n\t\t\t\t\tshareUrl: this.currentResult.shareUrl,\n\t\t\t\t\tshareImgUrl: this.currentResult.shareImgUrl,\n\t\t\t\t\ttimestamp: this.currentResult.timestamp\n\t\t\t\t};\n\t\t\t\tconsole.log(\"showLayoutModal\", this.currentLayoutResult);\n\n\n\t\t\t\t// 设置默认提示词\n\t\t\t\tthis.layoutPrompt = `请你对以下 HTML 内容进行排版优化，目标是用于微信公众号“草稿箱接口”的 content 字段，要求如下：\n\n1. 仅返回 <body> 内部可用的 HTML 内容片段（不要包含 <!DOCTYPE>、<html>、<head>、<meta>、<title> 等标签）。\n2. 所有样式必须以“内联 style”方式写入。\n3. 保持结构清晰、视觉友好，适配公众号图文排版。\n4. 请直接输出代码，不要添加任何注释或额外说明。\n5. 不得使用 emoji 表情符号或小图标字符。\n6. 不要显示为问答形式，以一篇文章的格式去调整 \\n\\n以下为需要进行排版优化的内容：\\n`+ this.currentResult.content ;\n\t\t\t\tthis.layoutModalVisible = true;\n\t\t\t},\n\n\t\t\tcloseLayoutModal() {\n\t\t\t\tthis.layoutModalVisible = false;\n\t\t\t},\n\n\t\t\thandleLayout() {\n\t\t\t\tif (this.layoutPrompt.trim().length === 0) return;\n\n\t\t\t\t// 构建智能排版请求\n\t\t\t\tconst layoutRequest = {\n\t\t\t\t\tjsonrpc: '2.0',\n\t\t\t\t\tid: this.generateUUID(),\n\t\t\t\t\tmethod: 'AI排版',\n\t\t\t\t\tparams: {\n\t\t\t\t\t\ttaskId: this.generateUUID(),\n\t\t\t\t\t\tuserId: this.userId,\n\t\t\t\t\t\tcorpId: this.corpId,\n\t\t\t\t\t\tuserPrompt: this.layoutPrompt,\n\t\t\t\t\t\troles: 'zj-db-sdsk' // 默认使用豆包进行排版\n\t\t\t\t\t}\n\t\t\t\t};\n\n\t\t\t\t// 发送排版请求\n\t\t\t\tconsole.log(\"智能排版参数\", layoutRequest);\n\t\t\t\tthis.message(layoutRequest);\n\t\t\t\tthis.closeLayoutModal();\n\n\t\t\t\t// 创建智能排版AI节点\n\t\t\t\tconst znpbAI = {\n\t\t\t\t\tname: '智能排版',\n\t\t\t\t\tavatar: 'https://u3w.com/chatfile/%E8%B1%86%E5%8C%85.png',\n\t\t\t\t\tcapabilities: [],\n\t\t\t\t\tselectedCapabilities: [],\n\t\t\t\t\tenabled: true,\n\t\t\t\t\tstatus: 'running',\n\t\t\t\t\tprogressLogs: [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tcontent: '智能排版任务已提交，正在排版...',\n\t\t\t\t\t\t\ttimestamp: new Date(),\n\t\t\t\t\t\t\tisCompleted: false,\n\t\t\t\t\t\t\ttype: '智能排版'\n\t\t\t\t\t\t}\n\t\t\t\t\t],\n\t\t\t\t\tisExpanded: true\n\t\t\t\t};\n\n\t\t\t\t// 检查是否已存在智能排版\n\t\t\t\tconst existIndex = this.enabledAIs.findIndex(ai => ai.name === '智能排版');\n\t\t\t\tif (existIndex === -1) {\n\t\t\t\t\t// 如果不存在，添加到数组开头\n\t\t\t\t\tthis.enabledAIs.unshift(znpbAI);\n\t\t\t\t} else {\n\t\t\t\t\t// 如果已存在，更新状态和日志\n\t\t\t\t\tthis.enabledAIs[existIndex] = znpbAI;\n\t\t\t\t\t// 将智能排版移到数组开头\n\t\t\t\t\tconst znpb = this.enabledAIs.splice(existIndex, 1)[0];\n\t\t\t\t\tthis.enabledAIs.unshift(znpb);\n\t\t\t\t}\n\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '排版请求已发送，请等待结果',\n\t\t\t\t\ticon: 'success'\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// 推送到公众号\n\t\t\tasync handlePushToWechat(contentText) {\n\t\t\t\ttry {\n\t\t\t\t\tconsole.log(\"handlePushToWechat 开始执行\", this.currentLayoutResult);\n\n\t\t\t\t\tif (!this.currentLayoutResult) {\n\t\t\t\t\t\tconsole.error(\"currentLayoutResult 为空，无法投递\");\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '投递失败：缺少原始结果信息',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\tuni.showLoading({\n\t\t\t\t\t\ttitle: '正在投递...'\n\t\t\t\t\t});\n\n\t\t\t\t\t// 自增计数器\n\t\t\t\t\tthis.collectNum++;\n\n\t\t\t\t\tconst params = {\n\t\t\t\t\t\tcontentText: contentText,\n\t\t\t\t\t\tuserId: this.userId,\n\t\t\t\t\t\tshareUrl: this.currentLayoutResult.shareUrl || '',\n\t\t\t\t\t\taiName: this.currentLayoutResult.aiName || '',\n\t\t\t\t\t\tnum: this.collectNum\n\t\t\t\t\t};\n\t\t\t\t\t\n\t\t\t\t\tconsole.log(\"投递参数\", params);\n\n\t\t\t\t\tconst res = await pushAutoOffice(params);\n\n\t\t\t\t\tuni.hideLoading();\n\n\t\t\t\t\tif (res.code === 200) {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: `投递成功(${this.collectNum})`,\n\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t});\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: res.message || '投递失败',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\tconsole.error('投递到公众号失败:', error);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '投递失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\n\t\t\ttoggleResultSelection(event) {\n\t\t\t\tconst values = event.detail.value;\n\t\t\t\tconsole.log('toggleResultSelection - 选中的values:', values);\n\t\t\t\tconsole.log('toggleResultSelection - 当前scorePrompt:', this.scorePrompt.trim());\n\t\t\t\tthis.selectedResults = values;\n\t\t\t\tconsole.log('toggleResultSelection - 更新后的selectedResults:', this.selectedResults);\n\t\t\t\tconsole.log('toggleResultSelection - canScore状态:', this.canScore);\n\t\t\t},\n\n\t\t\thandleScore() {\n\t\t\t\tif (!this.canScore) return;\n\n\t\t\t\t// 获取选中的结果内容并按照指定格式拼接\n\t\t\t\tconst selectedContents = this.results\n\t\t\t\t\t.filter(result => this.selectedResults.includes(result.aiName))\n\t\t\t\t\t.map(result => {\n\t\t\t\t\t\t// 将HTML内容转换为纯文本（小程序版本简化处理）\n\t\t\t\t\t\tconst plainContent = result.content.replace(/<[^>]*>/g, '');\n\t\t\t\t\t\treturn `${result.aiName}初稿：\\n${plainContent}\\n`;\n\t\t\t\t\t})\n\t\t\t\t\t.join('\\n');\n\n\t\t\t\t// 构建完整的评分提示内容\n\t\t\t\tconst fullPrompt = `${this.scorePrompt}\\n${selectedContents}`;\n\n\t\t\t\t// 构建评分请求\n\t\t\t\tconst scoreRequest = {\n\t\t\t\t\tjsonrpc: '2.0',\n\t\t\t\t\tid: this.generateUUID(),\n\t\t\t\t\tmethod: 'AI评分',\n\t\t\t\t\tparams: {\n\t\t\t\t\t\ttaskId: this.generateUUID(),\n\t\t\t\t\t\tuserId: this.userId,\n\t\t\t\t\t\tcorpId: this.corpId,\n\t\t\t\t\t\tuserPrompt: fullPrompt,\n\t\t\t\t\t\troles: 'zj-db-sdsk' // 默认使用豆包进行评分\n\t\t\t\t\t}\n\t\t\t\t};\n\n\t\t\t\t// 发送评分请求\n\t\t\t\tconsole.log(\"参数\", scoreRequest);\n\t\t\t\tthis.message(scoreRequest);\n\t\t\t\tthis.closeScoreModal();\n\n\t\t\t\t// 创建智能评分AI节点\n\t\t\t\tconst wkpfAI = {\n\t\t\t\t\tname: '智能评分',\n\t\t\t\t\tavatar: 'https://u3w.com/chatfile/%E8%B1%86%E5%8C%85.png',\n\t\t\t\t\tcapabilities: [],\n\t\t\t\t\tselectedCapabilities: [],\n\t\t\t\t\tenabled: true,\n\t\t\t\t\tstatus: 'running',\n\t\t\t\t\tprogressLogs: [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tcontent: '智能评分任务已提交，正在评分...',\n\t\t\t\t\t\t\ttimestamp: new Date(),\n\t\t\t\t\t\t\tisCompleted: false,\n\t\t\t\t\t\t\ttype: '智能评分'\n\t\t\t\t\t\t}\n\t\t\t\t\t],\n\t\t\t\t\tisExpanded: true\n\t\t\t\t};\n\n\t\t\t\t// 检查是否已存在智能评分\n\t\t\t\tconst existIndex = this.enabledAIs.findIndex(ai => ai.name === '智能评分');\n\t\t\t\tif (existIndex === -1) {\n\t\t\t\t\t// 如果不存在，添加到数组开头\n\t\t\t\t\tthis.enabledAIs.unshift(wkpfAI);\n\t\t\t\t} else {\n\t\t\t\t\t// 如果已存在，更新状态和日志\n\t\t\t\t\tthis.enabledAIs[existIndex] = wkpfAI;\n\t\t\t\t\t// 将智能评分移到数组开头\n\t\t\t\t\tconst wkpf = this.enabledAIs.splice(existIndex, 1)[0];\n\t\t\t\t\tthis.enabledAIs.unshift(wkpf);\n\t\t\t\t}\n\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '评分请求已发送，请等待结果',\n\t\t\t\t\ticon: 'success'\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// 创建新对话\n\t\t\tcreateNewChat() {\n\t\t\t\t// 重置所有数据\n\t\t\t\tthis.chatId = this.generateUUID();\n\t\t\t\tthis.promptInput = '';\n\t\t\t\tthis.taskStarted = false;\n\t\t\t\tthis.screenshots = [];\n\t\t\t\tthis.results = [];\n\t\t\t\tthis.enabledAIs = [];\n\t\t\t\tthis.userInfoReq = {\n\t\t\t\t\tuserPrompt: '',\n\t\t\t\t\tuserId: this.userId,\n\t\t\t\t\tcorpId: this.corpId,\n\t\t\t\t\ttaskId: '',\n\t\t\t\t\troles: '',\n\t\t\t\t\ttoneChatId: '',\n\t\t\t\t\tybDsChatId: '',\n\t\t\t\t\tdbChatId: '',\n\t\t\t\t\tisNewChat: true\n\t\t\t\t};\n\t\t\t\t// 重置AI列表为初始状态\n\t\t\t\tthis.aiList = [{\n\t\t\t\t\t\tname: 'AI搜索@元器',\n\t\t\t\t\t\tavatar: 'https://u3w.com/chatfile/yuanbao.png',\n\t\t\t\t\t\tcapabilities: [],\n\t\t\t\t\t\tselectedCapabilities: [],\n\t\t\t\t\t\tenabled: true,\n\t\t\t\t\t\tstatus: 'idle',\n\t\t\t\t\t\tprogressLogs: [],\n\t\t\t\t\t\tisExpanded: true\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: '数智化助手@元器',\n\t\t\t\t\t\tavatar: 'https://u3w.com/chatfile/yuanbao.png',\n\t\t\t\t\t\tcapabilities: [],\n\t\t\t\t\t\tselectedCapabilities: [],\n\t\t\t\t\t\tenabled: true,\n\t\t\t\t\t\tstatus: 'idle',\n\t\t\t\t\t\tprogressLogs: [],\n\t\t\t\t\t\tisExpanded: true\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: '腾讯元宝T1',\n\t\t\t\t\t\tavatar: 'https://u3w.com/chatfile/yuanbao.png',\n\t\t\t\t\t\tcapabilities: [{\n\t\t\t\t\t\t\t\tlabel: '深度思考',\n\t\t\t\t\t\t\t\tvalue: 'deep_thinking'\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tlabel: '联网搜索',\n\t\t\t\t\t\t\t\tvalue: 'web_search'\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t],\n\t\t\t\t\t\tselectedCapabilities: ['deep_thinking', 'web_search'],\n\t\t\t\t\t\tenabled: true,\n\t\t\t\t\t\tstatus: 'idle',\n\t\t\t\t\t\tprogressLogs: [],\n\t\t\t\t\t\tisExpanded: true\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: '腾讯元宝DS',\n\t\t\t\t\t\tavatar: 'https://u3w.com/chatfile/yuanbao.png',\n\t\t\t\t\t\tcapabilities: [{\n\t\t\t\t\t\t\t\tlabel: '深度思考',\n\t\t\t\t\t\t\t\tvalue: 'deep_thinking'\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tlabel: '联网搜索',\n\t\t\t\t\t\t\t\tvalue: 'web_search'\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t],\n\t\t\t\t\t\tselectedCapabilities: ['deep_thinking', 'web_search'],\n\t\t\t\t\t\tenabled: true,\n\t\t\t\t\t\tstatus: 'idle',\n\t\t\t\t\t\tprogressLogs: [],\n\t\t\t\t\t\tisExpanded: true\n\t\t\t\t\t},\n          {\n            name: 'DeepSeek',\n            avatar: 'https://communication.cn-nb1.rains3.com/Deepseek.png',\n            capabilities: [{\n              label: '深度思考',\n              value: 'deep_thinking'\n            },\n              {\n                label: '联网搜索',\n                value: 'web_search'\n              }\n            ],\n            selectedCapabilities: ['deep_thinking', 'web_search'],\n            enabled: true,\n            status: 'idle',\n            progressLogs: [],\n            isExpanded: true\n          },\n\t\t\t\t\t{\n\t\t\t\t\t\tname: '豆包',\n\t\t\t\t\t\tavatar: 'https://u3w.com/chatfile/%E8%B1%86%E5%8C%85.png',\n\t\t\t\t\t\tcapabilities: [{\n\t\t\t\t\t\t\tlabel: '深度思考',\n\t\t\t\t\t\t\tvalue: 'deep_thinking'\n\t\t\t\t\t\t}],\n\t\t\t\t\t\tselectedCapabilities: ['deep_thinking'],\n\t\t\t\t\t\tenabled: true,\n\t\t\t\t\t\tstatus: 'idle',\n\t\t\t\t\t\tprogressLogs: [],\n\t\t\t\t\t\tisExpanded: true\n\t\t\t\t\t}\n\t\t\t\t];\n\t\t\t\t// 不再根据AI登录状态更新AI启用状态，保持原有选择\n\n\t\t\t\t// 展开相关区域\n\t\t\t\tthis.sectionExpanded.aiConfig = true;\n\t\t\t\tthis.sectionExpanded.promptInput = true;\n\t\t\t\tthis.sectionExpanded.taskStatus = true;\n\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '已创建新对话',\n\t\t\t\t\ticon: 'success'\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// AI状态相关方法\n\t\t\tcheckAiLoginStatus() {\n\t\t\t\t// 延迟检查，确保WebSocket连接已建立\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.sendAiStatusCheck();\n\t\t\t\t\t// 不再更新AI启用状态，保持原有选择\n\t\t\t\t}, 2000);\n\t\t\t},\n\n\t\t\tsendAiStatusCheck() {\n\t\t\t\t// 检查腾讯元宝登录状态\n\t\t\t\tthis.sendWebSocketMessage({\n\t\t\t\t\ttype: 'PLAY_CHECK_YB_LOGIN',\n\t\t\t\t\tuserId: this.userId,\n\t\t\t\t\tcorpId: this.corpId\n\t\t\t\t});\n\n\t\t\t\t// 检查豆包登录状态\n\t\t\t\tthis.sendWebSocketMessage({\n\t\t\t\t\ttype: 'PLAY_CHECK_DB_LOGIN',\n\t\t\t\t\tuserId: this.userId,\n\t\t\t\t\tcorpId: this.corpId\n\t\t\t\t});\n\n\t\t\t\t// 检查智能体登录状态\n\t\t\t\tthis.sendWebSocketMessage({\n\t\t\t\t\ttype: 'PLAY_CHECK_AGENT_LOGIN',\n\t\t\t\t\tuserId: this.userId,\n\t\t\t\t\tcorpId: this.corpId\n\t\t\t\t});\n\n\n        // 检查DeepSeek登录状态\n        this.sendWebSocketMessage({\n          type: 'PLAY_CHECK_DEEPSEEK_LOGIN',\n          userId: this.userId,\n          corpId: this.corpId\n        });\n\t\t\t},\n\n\t\t\tgetPlatformIcon(type) {\n\t\t\t\tconst icons = {\n\t\t\t\t\tyuanbao: 'https://u3w.com/chatfile/yuanbao.png',\n\t\t\t\t\tdoubao: 'https://u3w.com/chatfile/%E8%B1%86%E5%8C%85.png',\n\t\t\t\t\tagent: 'https://u3w.com/chatfile/yuanbao.png'\n\t\t\t\t};\n\t\t\t\treturn icons[type] || '';\n\t\t\t},\n\n\t\t\tgetPlatformName(type) {\n\t\t\t\tconst names = {\n\t\t\t\t\tyuanbao: '腾讯元宝',\n\t\t\t\t\tdoubao: '豆包',\n\t\t\t\t\tagent: '智能体'\n\t\t\t\t};\n\t\t\t\treturn names[type] || '';\n\t\t\t},\n\n\n\n\n\n\t\t\trefreshAiStatus() {\n\t\t\t\t// 重置所有AI状态为加载中\n\t\t\t\tthis.isLoading = {\n\t\t\t\t\tyuanbao: true,\n\t\t\t\t\tdoubao: true,\n\t\t\t\t\tagent: true,\n          deepseek: true\n\t\t\t\t};\n\n\t\t\t\t// 重置登录状态\n\t\t\t\tthis.aiLoginStatus = {\n\t\t\t\t\tyuanbao: false,\n\t\t\t\t\tdoubao: false,\n\t\t\t\t\tagent: false,\n          deepseek: false\n\t\t\t\t};\n\n\t\t\t\t// 重置账户信息\n\t\t\t\tthis.accounts = {\n\t\t\t\t\tyuanbao: '',\n\t\t\t\t\tdoubao: '',\n\t\t\t\t\tagent: '',\n          deepseek: ''\n\t\t\t\t};\n\n\t\t\t\t// 显示刷新提示\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '正在刷新连接状态...',\n\t\t\t\t\ticon: 'loading',\n\t\t\t\t\tduration: 1500\n\t\t\t\t});\n\n\t\t\t\t// 重新建立WebSocket连接\n\t\t\t\tthis.closeWebSocket();\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.initWebSocket();\n\t\t\t\t\t// 延迟检查AI状态，确保WebSocket重新连接\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tthis.sendAiStatusCheck();\n\t\t\t\t\t}, 2000);\n\t\t\t\t}, 500);\n\t\t\t},\n\n\t\t\t// 判断AI是否已登录可用\n\t\t\tisAiLoginEnabled(ai) {\n\t\t\t\tswitch (ai.name) {\n\t\t\t\t\tcase 'AI搜索@元器':\n\t\t\t\t\tcase '数智化助手@元器':\n\t\t\t\t\t\treturn this.aiLoginStatus.agent; // 智能体登录状态\n\t\t\t\t\tcase '腾讯元宝T1':\n\t\t\t\t\tcase '腾讯元宝DS':\n\t\t\t\t\t\treturn this.aiLoginStatus.yuanbao; // 腾讯元宝登录状态\n\t\t\t\t\tcase '豆包':\n\t\t\t\t\t\treturn this.aiLoginStatus.doubao; // 豆包登录状态\n          case 'DeepSeek':\n            return this.aiLoginStatus.deepseek; // 使用实际的DeepSeek登录状态\n\t\t\t\t\tdefault:\n\t\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 判断AI是否在加载状态\n\t\t\tisAiInLoading(ai) {\n\t\t\t\tswitch (ai.name) {\n\t\t\t\t\tcase 'AI搜索@元器':\n\t\t\t\t\tcase '数智化助手@元器':\n\t\t\t\t\t\treturn this.isLoading.agent;\n\t\t\t\t\tcase '腾讯元宝T1':\n\t\t\t\t\tcase '腾讯元宝DS':\n\t\t\t\t\t\treturn this.isLoading.yuanbao;\n\t\t\t\t\tcase '豆包':\n\t\t\t\t\t\treturn this.isLoading.doubao;\n          case 'DeepSeek':\n            return this.isLoading.deepseek; // 使用实际的DeepSeek加载状态\n\t\t\t\t\tdefault:\n\t\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 根据登录状态禁用相关AI（已废弃，不再修改enabled状态）\n\t\t\tdisableAIsByLoginStatus(loginType) {\n\t\t\t\t// 不再修改enabled状态，只通过UI控制操作权限\n\t\t\t\tconsole.log(`AI ${loginType} 登录状态已更新，但保持原有选择`);\n\t\t\t},\n\n\t\t\t// 根据当前AI登录状态更新AI启用状态（已废弃，不再修改enabled状态）\n\t\t\tupdateAiEnabledStatus() {\n\t\t\t\t// 不再修改enabled状态，只通过UI控制操作权限\n\t\t\t\tconsole.log('AI登录状态已更新，但保持原有选择');\n\t\t\t},\n\n\t\t\t// 格式化时间\n\t\t\tformatTime(timestamp) {\n\t\t\t\ttry {\n\t\t\t\t\tconsole.log('formatTime 输入:', timestamp, typeof timestamp);\n\n\t\t\t\t\tif (!timestamp) {\n\t\t\t\t\t\treturn '时间未知';\n\t\t\t\t\t}\n\n\t\t\t\t\tlet date;\n\n\t\t\t\t\tif (typeof timestamp === 'number') {\n\t\t\t\t\t\tdate = new Date(timestamp);\n\t\t\t\t\t} else if (typeof timestamp === 'string') {\n\t\t\t\t\t\t// 处理ISO 8601格式：2025-06-25T07:18:54.110Z\n\t\t\t\t\t\tif (timestamp.includes('T') && (timestamp.includes('Z') || timestamp.includes('+'))) {\n\t\t\t\t\t\t\tdate = new Date(timestamp);\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// 处理 \"2025-6-23 14:53:12\" 这种格式\n\t\t\t\t\t\telse {\n\t\t\t\t\t\t\tconst match = timestamp.match(/(\\d{4})-(\\d{1,2})-(\\d{1,2})\\s+(\\d{1,2}):(\\d{1,2}):(\\d{1,2})/);\n\t\t\t\t\t\t\tif (match) {\n\t\t\t\t\t\t\t\tconst [, year, month, day, hour, minute, second] = match;\n\t\t\t\t\t\t\t\tdate = new Date(\n\t\t\t\t\t\t\t\t\tparseInt(year),\n\t\t\t\t\t\t\t\t\tparseInt(month) - 1,\n\t\t\t\t\t\t\t\t\tparseInt(day),\n\t\t\t\t\t\t\t\t\tparseInt(hour),\n\t\t\t\t\t\t\t\t\tparseInt(minute),\n\t\t\t\t\t\t\t\t\tparseInt(second)\n\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t// 如果正则不匹配，尝试其他方式\n\t\t\t\t\t\t\t\tconst fixedTimestamp = timestamp.replace(/\\s/g, 'T');\n\t\t\t\t\t\t\t\tdate = new Date(fixedTimestamp);\n\n\t\t\t\t\t\t\t\tif (isNaN(date.getTime())) {\n\t\t\t\t\t\t\t\t\tdate = new Date(timestamp);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t} else if (timestamp instanceof Date) {\n\t\t\t\t\t\tdate = timestamp;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tdate = new Date(timestamp);\n\t\t\t\t\t}\n\n\t\t\t\t\tconsole.log('formatTime 解析结果:', date, date.getTime());\n\n\t\t\t\t\tif (isNaN(date.getTime())) {\n\t\t\t\t\t\treturn '时间未知';\n\t\t\t\t\t}\n\n\t\t\t\t\t// 使用更简洁的时间格式，避免显示时区信息\n\t\t\t\t\tconst hour = date.getHours().toString().padStart(2, '0');\n\t\t\t\t\tconst minute = date.getMinutes().toString().padStart(2, '0');\n\t\t\t\t\tconst second = date.getSeconds().toString().padStart(2, '0');\n\n\t\t\t\t\tconst timeString = `${hour}:${minute}:${second}`;\n\n\t\t\t\t\tconsole.log('formatTime 输出:', timeString);\n\t\t\t\t\treturn timeString;\n\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('格式化时间错误:', error, timestamp);\n\t\t\t\t\treturn '时间未知';\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t};\n</script>\n\n<style scoped>\n\t.console-container {\n\t\theight: 100vh;\n\t\tbackground-color: #f5f7fa;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t}\n\n\t/* 顶部固定区域 */\n\t.header-fixed {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tz-index: 1000;\n\t\tbackground-color: #fff;\n\t\tborder-bottom: 1px solid #ebeef5;\n\t}\n\n\t.header-content {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tpadding: 10px 15px;\n\t\tpadding-top: calc(10px + var(--status-bar-height));\n\t}\n\n\t.header-title {\n\t\tfont-size: 18px;\n\t\tfont-weight: 600;\n\t\tcolor: #303133;\n\t}\n\n\t.header-actions {\n\t\tdisplay: flex;\n\t\tgap: 10px;\n\t}\n\n\t.action-btn {\n\t\twidth: 36px;\n\t\theight: 36px;\n\t\tborder-radius: 18px;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\ttransition: all 0.3s ease;\n\t\tposition: relative;\n\t\toverflow: hidden;\n\t}\n\n\t.action-btn:active {\n\t\ttransform: scale(0.92);\n\t\topacity: 0.7;\n\t}\n\n\t.action-icon {\n\t\tfont-size: 18px;\n\t\tcolor: #ffffff;\n\t\tfont-weight: 500;\n\t\ttext-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tz-index: 1;\n\t\tposition: relative;\n\t}\n\n\t.action-icon-img {\n\t\twidth: 20px;\n\t\theight: 20px;\n\t\tz-index: 1;\n\t\tposition: relative;\n\t}\n\n\t/* 创建新会话图标更大 */\n\t.new-chat-btn .action-icon-img {\n\t\twidth: 24px;\n\t\theight: 24px;\n\t}\n\n\t/* 移除渐变背景，使用原生图标 */\n\t.refresh-btn,\n\t.history-btn,\n\t.new-chat-btn {\n\t\tbackground: transparent;\n\t\tbox-shadow: none;\n\t}\n\n\n\n\t/* 主体滚动区域 */\n\t.main-scroll {\n\t\tflex: 1;\n\t\theight: calc(100vh - 52px - var(--status-bar-height));\n\t\tpadding-top: calc(52px + var(--status-bar-height));\n\t\tpadding-bottom: 20px;\n\t\tbox-sizing: border-box;\n\t}\n\n\t/* 区块样式 */\n\t.section-block {\n\t\tmargin: 10px 15px;\n\t\tbackground-color: #fff;\n\t\tborder-radius: 8px;\n\t\toverflow: hidden;\n\t\tbox-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n\t}\n\n\t.section-header {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tpadding: 15px;\n\t\tborder-bottom: 1px solid #ebeef5;\n\t\tbackground-color: #fafafa;\n\t}\n\n\t.section-title {\n\t\tfont-size: 16px;\n\t\tfont-weight: 600;\n\t\tcolor: #303133;\n\t}\n\n\t.section-arrow {\n\t\tfont-size: 14px;\n\t\tcolor: #909399;\n\t\ttransition: transform 0.3s;\n\t}\n\n\t.task-arrow {\n\t\tfont-size: 12px;\n\t\tcolor: #909399;\n\t\ttransition: transform 0.3s;\n\t\tmargin-right: 8px;\n\t}\n\n\t.close-icon {\n\t\tfont-size: 18px;\n\t\tcolor: #909399;\n\t\tcursor: pointer;\n\t}\n\n\t.section-content {\n\t\tpadding: 15px;\n\t}\n\n\t/* AI配置区域 */\n\t.ai-grid {\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t\tgap: 10px;\n\t}\n\n\t.ai-card {\n\t\twidth: calc(50% - 5px);\n\t\tborder: 1px solid #ebeef5;\n\t\tborder-radius: 8px;\n\t\tpadding: 10px;\n\t\ttransition: all 0.3s;\n\t\tmin-height: 65px;\n\t\tbox-sizing: border-box;\n\t}\n\n\t.ai-card.ai-enabled {\n\t\tborder-color: #409EFF;\n\t\tbackground-color: #f0f8ff;\n\t}\n\n\t.ai-card.ai-disabled {\n\t\tbackground-color: #fafafa;\n\t\tborder-color: #e4e7ed;\n\t\tborder-style: dashed;\n\t\tpointer-events: none;\n\t}\n\n\t.ai-avatar.avatar-disabled {\n\t\topacity: 0.7;\n\t\tfilter: grayscale(30%);\n\t}\n\n\t.ai-name.name-disabled {\n\t\tcolor: #373839;\n\t}\n\n\t.login-required {\n\t\tfont-size: 9px;\n\t\tcolor: red;\n\t\tmargin-top: 2px;\n\t\tline-height: 1;\n\t}\n\n\t.loading-text {\n\t\tfont-size: 9px;\n\t\tcolor: #409EFF;\n\t\tmargin-top: 2px;\n\t\tline-height: 1;\n\t}\n\n\t.capability-tag.capability-disabled {\n\t\topacity: 0.5;\n\t\tbackground-color: #f5f5f5;\n\t\tborder-color: #e4e7ed;\n\t\tpointer-events: none;\n\t}\n\n\t.capability-tag.capability-disabled .capability-text {\n\t\tcolor: #c0c4cc;\n\t}\n\n\t.ai-header {\n\t\tdisplay: flex;\n\t\talign-items: flex-start;\n\t\tmargin-bottom: 8px;\n\t\tmin-height: 24px;\n\t}\n\n\t.ai-avatar {\n\t\twidth: 24px;\n\t\theight: 24px;\n\t\tborder-radius: 12px;\n\t\tmargin-right: 8px;\n\t}\n\n\t.ai-info {\n\t\tflex: 1;\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t}\n\n\t.ai-name-container {\n\t\tflex: 1;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: flex-start;\n\t\tmin-width: 0;\n\t}\n\n\t.ai-name {\n\t\tfont-size: 12px;\n\t\tfont-weight: 500;\n\t\tcolor: #303133;\n\t\twhite-space: nowrap;\n\t\toverflow: hidden;\n\t\ttext-overflow: ellipsis;\n\t\tmax-width: 100%;\n\t}\n\n\t.ai-capabilities {\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t\tgap: 4px;\n\t}\n\n\t.capability-tag {\n\t\tpadding: 2px 6px;\n\t\tborder-radius: 10px;\n\t\tborder: 1px solid #dcdfe6;\n\t\tbackground-color: #fff;\n\t\ttransition: all 0.3s;\n\t}\n\n\t.capability-tag.capability-active {\n\t\tbackground-color: #409EFF;\n\t\tborder-color: #409EFF;\n\t}\n\n\t.capability-text {\n\t\tfont-size: 10px;\n\t\tcolor: #606266;\n\t}\n\n\t.capability-tag.capability-active .capability-text {\n\t\tcolor: #fff;\n\t}\n\n\t/* 提示词输入区域 */\n\t.prompt-textarea {\n\t\twidth: 100%;\n\t\tmin-height: 80px;\n\t\tpadding: 10px;\n\t\tborder: 1px solid #dcdfe6;\n\t\tborder-radius: 4px;\n\t\tfont-size: 14px;\n\t\tline-height: 1.5;\n\t\tresize: none;\n\t\tbox-sizing: border-box;\n\t}\n\n\t.prompt-footer {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tmargin-top: 10px;\n\t}\n\n\t.word-count {\n\t\tfont-size: 12px;\n\t\tcolor: #909399;\n\t}\n\n\t.send-btn {\n\t\tbackground-color: #409EFF;\n\t\tcolor: #fff;\n\t\tborder: none;\n\t\tborder-radius: 20px;\n\t\tpadding: 6px 0;\n\t\tfont-size: 14px;\n\t\twidth: 50%;\n\t\theight: 30px;\n\t\tdisplay: flex;\n\t\tmargin-left: 50%;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t}\n\n\t.send-btn-disabled {\n\t\tbackground-color: #c0c4cc;\n\t}\n\n\t/* 任务执行状态 */\n\t.task-flow {\n\t\tmargin-bottom: 15px;\n\t}\n\n\t.task-item {\n\t\tborder: 1px solid #ebeef5;\n\t\tborder-radius: 8px;\n\t\tmargin-bottom: 10px;\n\t\toverflow: hidden;\n\t}\n\n\t.task-header {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tpadding: 12px;\n\t\tbackground-color: #fafafa;\n\t\tborder-bottom: 1px solid #ebeef5;\n\t}\n\n\t.task-left {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: 8px;\n\t}\n\n\t.task-avatar {\n\t\twidth: 20px;\n\t\theight: 20px;\n\t\tborder-radius: 10px;\n\t}\n\n\t.task-name {\n\t\tfont-size: 14px;\n\t\tfont-weight: 500;\n\t\tcolor: #303133;\n\t}\n\n\t.task-right {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: 5px;\n\t}\n\n\t.status-text {\n\t\tfont-size: 12px;\n\t\tcolor: #606266;\n\t}\n\n\t.status-icon {\n\t\tfont-size: 14px;\n\t}\n\n\t.status-completed {\n\t\tcolor: #67c23a;\n\t}\n\n\t.status-failed {\n\t\tcolor: #f56c6c;\n\t}\n\n\t.status-running {\n\t\tcolor: #409EFF;\n\t\tanimation: rotate 1s linear infinite;\n\t}\n\n\t.status-idle {\n\t\tcolor: #909399;\n\t}\n\n\t@keyframes rotate {\n\t\tfrom {\n\t\t\ttransform: rotate(0deg);\n\t\t}\n\n\t\tto {\n\t\t\ttransform: rotate(360deg);\n\t\t}\n\t}\n\n\t/* 进度日志 */\n\t.progress-logs {\n\t\tpadding: 10px 15px;\n\t\tmax-height: 150px;\n\t\toverflow-y: auto;\n\t}\n\n\t.progress-item {\n\t\tdisplay: flex;\n\t\talign-items: flex-start;\n\t\tmargin-bottom: 8px;\n\t\tposition: relative;\n\t}\n\n\t.progress-item:last-child {\n\t\tmargin-bottom: 0;\n\t}\n\n\t.progress-dot {\n\t\twidth: 8px;\n\t\theight: 8px;\n\t\tborder-radius: 4px;\n\t\tbackground-color: #dcdfe6;\n\t\tmargin-right: 10px;\n\t\tmargin-top: 6px;\n\t\tflex-shrink: 0;\n\t}\n\n\t.progress-dot.dot-completed {\n\t\tbackground-color: #67c23a;\n\t}\n\n\t.progress-content {\n\t\tflex: 1;\n\t\tmin-width: 0;\n\t}\n\n\t.progress-time {\n\t\tfont-size: 10px;\n\t\tcolor: #909399;\n\t\tmargin-bottom: 2px;\n\t}\n\n\t.progress-text {\n\t\tfont-size: 12px;\n\t\tcolor: #606266;\n\t\tline-height: 1.4;\n\t\tword-break: break-all;\n\t}\n\n\t/* 主机可视化 */\n\t.screenshots-section {\n\t\tmargin-top: 15px;\n\t}\n\n\t.screenshots-header {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-bottom: 10px;\n\t\tgap: 10px;\n\t}\n\n\t.section-subtitle {\n\t\tfont-size: 14px;\n\t\tfont-weight: 500;\n\t\tcolor: #303133;\n\t}\n\n\t.auto-play-text {\n\t\tfont-size: 12px;\n\t\tcolor: #606266;\n\t}\n\n\t.screenshots-swiper {\n\t\theight: 200px;\n\t\tborder-radius: 8px;\n\t\toverflow: hidden;\n\t}\n\n\t.screenshot-image {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t}\n\n\t/* 结果展示区域 - 简洁标签页风格 */\n\n\t.result-tabs {\n\t\twhite-space: nowrap;\n\t\tmargin-bottom: 20px;\n\t\tborder-bottom: 1px solid #ebeef5;\n\t}\n\n\t.tab-container {\n\t\tdisplay: flex;\n\t\tgap: 0;\n\t\tpadding: 0 15px;\n\t}\n\n\t.result-tab {\n\t\tflex-shrink: 0;\n\t\tpadding: 12px 20px;\n\t\tposition: relative;\n\t\ttransition: all 0.3s ease;\n\t\tbackground: transparent;\n\t\tborder: none;\n\t}\n\n\t.result-tab::after {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\tbottom: 0;\n\t\tleft: 50%;\n\t\twidth: 0;\n\t\theight: 2px;\n\t\tbackground: #409EFF;\n\t\ttransition: all 0.3s ease;\n\t\ttransform: translateX(-50%);\n\t}\n\n\t.result-tab.tab-active::after {\n\t\twidth: 80%;\n\t}\n\n\t.tab-text {\n\t\tfont-size: 14px;\n\t\tcolor: #909399;\n\t\tfont-weight: 400;\n\t\ttransition: all 0.3s ease;\n\t\twhite-space: nowrap;\n\t}\n\n\t.result-tab.tab-active .tab-text {\n\t\tcolor: #409EFF;\n\t\tfont-weight: 500;\n\t}\n\n\t.result-tab:active {\n\t\ttransform: translateY(1px);\n\t}\n\n\t.result-content {\n\t\tmargin-top: 10px;\n\t}\n\n\t.result-header {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tmargin-bottom: 10px;\n\t\tpadding-bottom: 8px;\n\t\tborder-bottom: 1px solid #ebeef5;\n\t}\n\n\t.result-title {\n\t\tfont-size: 14px;\n\t\tfont-weight: 500;\n\t\tcolor: #303133;\n\t}\n\n\n\n\t.result-body {\n\t\tmargin-bottom: 15px;\n\t}\n\n\t.result-image-container {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t}\n\n\t.result-image {\n\t\tmax-width: 100%;\n\t\tborder-radius: 8px;\n\t}\n\n\t/* PDF文件容器样式 */\n\t.result-pdf-container {\n\t\tbackground-color: #f9f9f9;\n\t\tborder-radius: 8px;\n\t\tborder: 2px dashed #dcdfe6;\n\t\toverflow: hidden;\n\t}\n\n\t.pdf-placeholder {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tpadding: 20px;\n\t}\n\n\t.pdf-icon {\n\t\tfont-size: 48px;\n\t\tmargin-bottom: 10px;\n\t}\n\n\t.pdf-text {\n\t\tfont-size: 14px;\n\t\tcolor: #606266;\n\t\tmargin-bottom: 15px;\n\t}\n\n\t.pdf-actions {\n\t\tdisplay: flex;\n\t\tgap: 10px;\n\t\tjustify-content: center;\n\t}\n\n\t.pdf-btn {\n\t\tborder-radius: 4px;\n\t\tpadding: 8px 16px;\n\t\tfont-size: 12px;\n\t\theight: auto;\n\t\tline-height: 1.2;\n\t\tflex: 1;\n\t\tmax-width: 100px;\n\t}\n\n\t.download-btn {\n\t\tbackground-color: #f6ffed;\n\t\tcolor: #52c41a;\n\t\tborder: 1px solid #b7eb8f;\n\t}\n\n\t.copy-btn {\n\t\tbackground-color: #fff7e6;\n\t\tcolor: #fa8c16;\n\t\tborder: 1px solid #ffd591;\n\t}\n\n\t.result-text {\n\t\tpadding: 10px;\n\t\tbackground-color: #f9f9f9;\n\t\tborder-radius: 8px;\n\t\tfont-size: 14px;\n\t\tline-height: 1.6;\n\t\tmax-height: 300px;\n\t\toverflow-y: auto;\n\t}\n\n\t.result-actions {\n\t\tdisplay: flex;\n\t\tjustify-content: flex-end;\n\t\tgap: 8px;\n\t\tflex-wrap: wrap;\n\t\tmargin-bottom: 15px;\n\t}\n\n\t.action-btn-small, .share-link-btn, .collect-btn {\n\t\tborder: 1px solid #dcdfe6;\n\t\tborder-radius: 12px;\n\t\tpadding: 4px 12px;\n\t\tfont-size: 12px;\n\t\theight: auto;\n\t\tline-height: 1.2;\n\t\tmin-width: 60px;\n\t\ttext-align: center;\n\t\ttransition: all 0.3s ease;\n\t}\n\n\t.action-btn-small {\n\t\tbackground-color: #f5f7fa;\n\t\tcolor: #606266;\n\t\tborder-color: #dcdfe6;\n\t}\n\n\t.share-link-btn {\n\t\tbackground-color: #67c23a;\n\t\tcolor: #fff;\n\t\tborder-color: #67c23a;\n\t}\n\n\t.collect-btn {\n\t\tbackground-color: #e6a23c;\n\t\tcolor: #fff;\n\t\tborder-color: #e6a23c;\n\t}\n\n\t/* 按钮悬停和点击效果 */\n\t.action-btn-small:active {\n\t\topacity: 0.8;\n\t\ttransform: scale(0.95);\n\t}\n\n\t.share-link-btn:active {\n\t\topacity: 0.8;\n\t\ttransform: scale(0.95);\n\t}\n\n\t.collect-btn:active {\n\t\topacity: 0.8;\n\t\ttransform: scale(0.95);\n\t}\n\n\t/* 智能评分按钮在标题栏 */\n\t.score-btn {\n\t\tbackground-color: #409EFF;\n\t\tcolor: #fff;\n\t\tborder: none;\n\t\tborder-radius: 12px;\n\t\tpadding: 4px 12px;\n\t\tfont-size: 12px;\n\t\theight: auto;\n\t\tline-height: 1.2;\n\t\tmargin-left: 57%;\n\t\tflex-shrink: 0;\n\t}\n\n\t/* 历史记录抽屉 */\n\t.drawer-mask {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tbackground-color: rgba(0, 0, 0, 0.5);\n\t\tz-index: 999;\n\t\tdisplay: flex;\n\t\tjustify-content: flex-end;\n\t}\n\n\t.drawer-container {\n\t\twidth: 280px;\n\t\theight: 100vh;\n\t\tbackground-color: #fff;\n\t}\n\n\t.drawer-content {\n\t\tmargin-top: 120rpx;\n\t\theight: 100vh;\n\t\tbackground-color: #fff;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tbox-sizing: border-box;\n\t}\n\n\t.drawer-header {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tpadding: 15px;\n\t\tborder-bottom: 1px solid #ebeef5;\n\t}\n\n\t.drawer-title {\n\t\tfont-size: 16px;\n\t\tfont-weight: 600;\n\t\tcolor: #303133;\n\t}\n\n\t.history-list {\n\t\tflex: 1;\n\t\tpadding: 10px;\n\t\theight: calc(100vh - 60px);\n\t\tbox-sizing: border-box;\n\t}\n\n\t.history-group {\n\t\tmargin-bottom: 15px;\n\t}\n\n\t.history-date {\n\t\tfont-size: 12px;\n\t\tcolor: #909399;\n\t\tmargin-bottom: 8px;\n\t\tpadding: 5px 0;\n\t\tborder-bottom: 1px solid #f0f0f0;\n\t}\n\n\t.history-item {\n\t\tbackground-color: #f9f9f9;\n\t\tborder-radius: 8px;\n\t\tpadding: 10px;\n\t\tmargin-bottom: 8px;\n\t}\n\n\t.history-prompt {\n\t\tfont-size: 14px;\n\t\tcolor: #303133;\n\t\tline-height: 1.4;\n\t\tmargin-bottom: 5px;\n\t\tdisplay: -webkit-box;\n\t\t-webkit-line-clamp: 2;\n\t\t-webkit-box-orient: vertical;\n\t\toverflow: hidden;\n\t}\n\n\t.history-time {\n\t\tfont-size: 10px;\n\t\tcolor: #909399;\n\t}\n\n\t/* 智能评分弹窗 */\n\t.popup-mask {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tbackground-color: rgba(0, 0, 0, 0.5);\n\t\tz-index: 999;\n\t\tdisplay: flex;\n\t\talign-items: flex-end;\n\t}\n\n\t.score-modal {\n\t\twidth: 100%;\n\t\tbackground-color: #fff;\n\t\tborder-radius: 20px 20px 0 0;\n\t\tmax-height: 80vh;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t}\n\n\t.score-header {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tpadding: 15px;\n\t\tborder-bottom: 1px solid #ebeef5;\n\t}\n\n\t.score-title {\n\t\tfont-size: 16px;\n\t\tfont-weight: 600;\n\t\tcolor: #303133;\n\t}\n\n\t.score-content {\n\t\tflex: 1;\n\t\tpadding: 15px;\n\t\toverflow-y: auto;\n\t}\n\n\t.score-selection {\n\t\tmargin-bottom: 20px;\n\t}\n\n\t.score-subtitle {\n\t\tfont-size: 14px;\n\t\tfont-weight: 500;\n\t\tcolor: #303133;\n\t\tmargin-bottom: 10px;\n\t}\n\n\t.score-checkboxes {\n\t\tmargin-top: 30rpx;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: 8px;\n\t}\n\n\t.checkbox-item {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: 8px;\n\t}\n\n\t.checkbox-text {\n\t\tfont-size: 14px;\n\t\tcolor: #606266;\n\t}\n\n\t.score-prompt-section {\n\t\tmargin-bottom: 20px;\n\t}\n\n\t.score-textarea {\n\t\twidth: 100%;\n\t\theight: 120px;\n\t\tpadding: 10px;\n\t\tborder: 1px solid #dcdfe6;\n\t\tborder-radius: 8px;\n\t\tfont-size: 14px;\n\t\tresize: none;\n\t\tbox-sizing: border-box;\n\t\tmargin-top: 10px;\n\t}\n\n\t.score-submit-btn {\n\t\twidth: 100%;\n\t\tbackground-color: #409EFF;\n\t\tcolor: #fff;\n\t\tborder: none;\n\t\tborder-radius: 8px;\n\t\tpadding: 12px;\n\t\tfont-size: 16px;\n\t}\n\n\t.score-submit-btn[disabled] {\n\t\tbackground-color: #c0c4cc;\n\t}\n\n\t/* 响应式布局 */\n\t@media (max-width: 375px) {\n\t\t.ai-card {\n\t\t\twidth: 100%;\n\t\t}\n\n\t\t.header-content {\n\t\t\tpadding: 8px 12px;\n\t\t}\n\n\t\t.section-block {\n\t\t\tmargin: 8px 12px;\n\t\t}\n\t}\n\n\t/* 响应式布局 */\n\t@media (max-width: 375px) {\n\t\t.ai-card {\n\t\t\twidth: 100%;\n\t\t}\n\n\t\t.header-content {\n\t\t\tpadding: 8px 12px;\n\t\t}\n\n\t\t.section-block {\n\t\t\tmargin: 8px 12px;\n\t\t}\n\t}\n\n  /* DeepSeek响应内容的特定样式 */\n  .deepseek-format-container {\n    margin: 20px 0;\n    padding: 15px;\n    background-color: #f9f9f9;\n    border-radius: 5px;\n    border: 1px solid #eaeaea;\n  }\n\n  .result-text .deepseek-response {\n    max-width: 100%;\n    margin: 0 auto;\n    background-color: #fff;\n    border-radius: 8px;\n    padding: 10px;\n    font-family: Arial, sans-serif;\n  }\n\n  .result-text .deepseek-response pre {\n    background-color: #f5f5f5;\n    padding: 10px;\n    border-radius: 4px;\n    font-family: monospace;\n    overflow-x: auto;\n    display: block;\n    margin: 10px 0;\n    font-size: 12px;\n  }\n\n  .result-text .deepseek-response code {\n    background-color: #f5f5f5;\n    padding: 2px 4px;\n    border-radius: 3px;\n    font-family: monospace;\n    font-size: 12px;\n  }\n\n  .result-text .deepseek-response table {\n    border-collapse: collapse;\n    width: 100%;\n    margin: 15px 0;\n  }\n\n  .result-text .deepseek-response th,\n  .result-text .deepseek-response td {\n    border: 1px solid #ddd;\n    padding: 8px;\n    text-align: left;\n    font-size: 12px;\n  }\n\n  .result-text .deepseek-response th {\n    background-color: #f2f2f2;\n    font-weight: bold;\n  }\n\n  .result-text .deepseek-response h1,\n  .result-text .deepseek-response h2,\n  .result-text .deepseek-response h3,\n  .result-text .deepseek-response h4,\n  .result-text .deepseek-response h5,\n  .result-text .deepseek-response h6 {\n    margin-top: 20px;\n    margin-bottom: 10px;\n    font-weight: bold;\n    color: #222;\n  }\n\n  .result-text .deepseek-response a {\n    color: #0066cc;\n    text-decoration: none;\n  }\n\n  .result-text .deepseek-response blockquote {\n    border-left: 4px solid #ddd;\n    padding-left: 15px;\n    margin: 15px 0;\n    color: #555;\n  }\n\n  .result-text .deepseek-response ul,\n  .result-text .deepseek-response ol {\n    padding-left: 20px;\n    margin: 10px 0;\n  }\n</style>\n", "import mod from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=51b5538d&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=51b5538d&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752133488514\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}